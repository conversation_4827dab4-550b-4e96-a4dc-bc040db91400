#!/usr/bin/env python3
"""
批量处理Point Deformable Mamba完整模型流程可视化
"""

import os
import sys
import argparse
from model_pipeline_visualization import ModelPipelineVisualizer

def create_batch_summary_html(output_dir, processed_samples):
    """创建批量处理的总结HTML页面"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 批量流程可视化结果</title>
        <style>
            body {{ 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                margin: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }}
            .container {{ 
                max-width: 1400px; 
                margin: 0 auto; 
                background-color: white; 
                padding: 30px; 
                border-radius: 15px; 
                box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            }}
            .header {{
                text-align: center;
                margin-bottom: 40px;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
            }}
            .sample-grid {{ 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); 
                gap: 30px; 
                margin-bottom: 40px;
            }}
            .sample-card {{ 
                border: 2px solid #e0e0e0; 
                padding: 25px; 
                border-radius: 12px; 
                background: linear-gradient(145deg, #f8f9fa, #e9ecef);
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }}
            .sample-card:hover {{
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }}
            .sample-title {{
                font-size: 20px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
                padding: 10px;
                background: white;
                border-radius: 8px;
            }}
            .file-links {{ 
                display: flex; 
                flex-direction: column; 
                gap: 12px; 
            }}
            .file-link {{ 
                display: block; 
                padding: 12px 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                text-decoration: none; 
                color: white; 
                border-radius: 8px; 
                text-align: center;
                font-weight: 500;
                transition: all 0.3s ease;
            }}
            .file-link:hover {{ 
                transform: scale(1.05);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            }}
            .stats-section {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 30px;
            }}
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }}
            .stat-item {{
                text-align: center;
                padding: 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .stat-number {{
                font-size: 24px;
                font-weight: bold;
                color: #667eea;
            }}
            .stat-label {{
                color: #666;
                margin-top: 5px;
            }}
            .pipeline-info {{
                background: #e3f2fd;
                padding: 20px;
                border-radius: 10px;
                border-left: 5px solid #2196f3;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔬 Point Deformable Mamba 批量流程可视化</h1>
                <p>完整的模型处理流程：ScanObjectNN → FPS采样 → 模型处理 → 扫描可视化</p>
            </div>
            
            <div class="stats-section">
                <h2>📊 处理统计</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{len(processed_samples)}</div>
                        <div class="stat-label">处理样本数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2048</div>
                        <div class="stat-label">原始点数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1024</div>
                        <div class="stat-label">FPS采样点数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">256</div>
                        <div class="stat-label">分组中心点数</div>
                    </div>
                </div>
            </div>
            
            <div class="pipeline-info">
                <h3>🔄 处理流程说明</h3>
                <ol>
                    <li><strong>数据加载</strong>: 从ScanObjectNN数据集加载原始点云（2048个点）</li>
                    <li><strong>预处理</strong>: 中心化和归一化处理</li>
                    <li><strong>FPS采样</strong>: 使用最远点采样减少到1024个点</li>
                    <li><strong>模型处理</strong>: 通过PointDeformableMamba模型进行分组和特征提取</li>
                    <li><strong>扫描可视化</strong>: 展示希尔伯特、反希尔伯特和可变形扫描方法</li>
                </ol>
            </div>
            
            <h2>📁 样本可视化结果</h2>
            <div class="sample-grid">
    """
    
    for sample_info in processed_samples:
        sample_idx, label = sample_info
        html_content += f"""
                <div class="sample-card">
                    <div class="sample-title">样本 {sample_idx} (标签: {label})</div>
                    <div class="file-links">
                        <a href="pipeline_sample_{sample_idx}.html" target="_blank" class="file-link">
                            🌐 交互式Web可视化
                        </a>
                        <a href="pipeline_sample_{sample_idx}.png" target="_blank" class="file-link">
                            🖼️ 高分辨率图片
                        </a>
                    </div>
                </div>
        """
    
    html_content += """
            </div>
            
            <div class="pipeline-info">
                <h3>💡 使用说明</h3>
                <ul>
                    <li><strong>Web可视化</strong>: 点击"交互式Web可视化"链接，可以旋转、缩放观察3D点云</li>
                    <li><strong>图片查看</strong>: 点击"高分辨率图片"链接查看静态图片，适合保存和分享</li>
                    <li><strong>流程对比</strong>: 每个可视化都包含6个步骤的完整展示</li>
                    <li><strong>扫描分析</strong>: 观察不同扫描方法对同一点云的处理差异</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Point Deformable Mamba 可视化工具</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    with open(os.path.join(output_dir, "batch_summary.html"), "w", encoding="utf-8") as f:
        f.write(html_content)

def main():
    parser = argparse.ArgumentParser(description='批量处理Point Deformable Mamba完整模型流程可视化')
    parser.add_argument('--data_path', type=str, default='data/ScanObjectNN/main_split_nobg',
                        help='ScanObjectNN数据集路径')
    parser.add_argument('--start_idx', type=int, default=0,
                        help='起始样本索引')
    parser.add_argument('--num_samples', type=int, default=3,
                        help='处理样本数量')
    parser.add_argument('--npoints', type=int, default=1024,
                        help='FPS采样点数')
    parser.add_argument('--output_dir', type=str, default='batch_pipeline_output',
                        help='输出目录')
    parser.add_argument('--device', type=str, default='cuda',
                        help='计算设备')
    
    args = parser.parse_args()
    
    print("Point Deformable Mamba 批量模型流程可视化")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建可视化器
    visualizer = ModelPipelineVisualizer(data_path=args.data_path, device=args.device)
    
    processed_samples = []
    
    try:
        for i in range(args.num_samples):
            sample_idx = args.start_idx + i
            print(f"\n{'='*20} 处理样本 {sample_idx} ({i+1}/{args.num_samples}) {'='*20}")
            
            # 1. 加载原始数据
            print(f"步骤1: 从ScanObjectNN加载样本 {sample_idx}")
            original_points, label = visualizer.load_scanobjectnn_sample(sample_idx)
            if original_points is None:
                print(f"跳过样本 {sample_idx}")
                continue
            
            # 2. 标准化
            print("步骤2: 标准化点云")
            original_points = visualizer.normalize_point_cloud(original_points)
            
            # 3. FPS采样
            print(f"步骤3: 应用FPS采样到 {args.npoints} 个点")
            fps_points = visualizer.apply_fps_sampling(original_points, args.npoints)
            
            # 4. 通过模型处理
            print("步骤4: 通过PointDeformableMamba模型处理")
            model_result = visualizer.process_through_model(fps_points)
            
            # 5. 生成可视化
            print("步骤5: 生成可视化结果")
            
            # Web可视化
            visualizer.create_pipeline_visualization(
                original_points, fps_points, model_result,
                title_prefix=f"Sample {sample_idx} (Label: {label}) - ",
                save_path=os.path.join(args.output_dir, f"pipeline_sample_{sample_idx}.html")
            )
            
            # 图片可视化
            visualizer.create_pipeline_image(
                original_points, fps_points, model_result,
                title_prefix=f"Sample {sample_idx} (Label: {label}) - ",
                save_path=os.path.join(args.output_dir, f"pipeline_sample_{sample_idx}.png")
            )
            
            processed_samples.append((sample_idx, label))
            print(f"✅ 样本 {sample_idx} 处理完成")
        
        # 创建批量总结页面
        if processed_samples:
            create_batch_summary_html(args.output_dir, processed_samples)
            print(f"\n🎉 批量处理完成！")
            print(f"📁 输出目录: {args.output_dir}")
            print(f"🌐 总结页面: batch_summary.html")
            print(f"📊 成功处理 {len(processed_samples)} 个样本")
        else:
            print("❌ 没有成功处理任何样本")
        
    except Exception as e:
        print(f"❌ 批量处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
