#!/usr/bin/env python3
"""
快速生成单个样本的可视化图片
"""

import os
import sys
import numpy as np
import torch
import h5py
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 尝试导入模型相关模块
try:
    from models.point_mamba_scan import serialization_func
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False

def load_sample_data(data_path="data/ScanObjectNN/main_split_nobg", sample_idx=0):
    """加载单个样本数据"""
    h5_file = os.path.join(data_path, "test_objectdataset.h5")
    
    if not os.path.exists(h5_file):
        print(f"数据文件不存在: {h5_file}")
        return None, None
        
    with h5py.File(h5_file, 'r') as f:
        points = np.array(f['data'][sample_idx]).astype(np.float32)
        label = np.array(f['label'][sample_idx]).astype(int)
        
    return points, label

def normalize_points(points):
    """标准化点云"""
    centroid = np.mean(points, axis=0)
    points = points - centroid
    max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
    points = points / max_dist
    return points

def simple_hilbert_order(points, grid_size=0.02):
    """简化的希尔伯特排序"""
    scaled_coord = points / grid_size
    grid_coord = np.floor(scaled_coord).astype(np.int64)
    
    def z_order_key(coord):
        x, y, z = coord
        return (x & 0xFF) | ((y & 0xFF) << 8) | ((z & 0xFF) << 16)
    
    z_values = [z_order_key(coord) for coord in grid_coord]
    return np.argsort(z_values)

def get_scanning_orders(points):
    """获取不同的扫描顺序"""
    if MODELS_AVAILABLE:
        try:
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            features = torch.randn(1, points.shape[0], 192)
            pos_embed = torch.randn(1, points.shape[0], 192)
            
            # 希尔伯特扫描
            _, hilbert_order, _, _, _ = serialization_func(
                points_tensor, features, pos_embed, 'hilbert'
            )
            
            # 反希尔伯特扫描
            _, hilbert_trans_order, _, _, _ = serialization_func(
                points_tensor, features, pos_embed, 'hilbert-trans'
            )
            
            hilbert_order = hilbert_order.squeeze(0).numpy()
            hilbert_trans_order = hilbert_trans_order.squeeze(0).numpy()
            
        except Exception as e:
            print(f"使用真实模型时出错: {e}")
            hilbert_order = simple_hilbert_order(points)
            hilbert_trans_order = hilbert_order[::-1]
    else:
        hilbert_order = simple_hilbert_order(points)
        hilbert_trans_order = hilbert_order[::-1]
    
    # 可变形扫描（简化版本：随机顺序）
    deform_order = np.arange(len(points))
    np.random.shuffle(deform_order)
    
    return hilbert_order, hilbert_trans_order, deform_order

def create_single_scanning_image(points, scan_order, title, save_path, max_points=100):
    """创建单个扫描方法的图片"""
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 限制显示点数
    display_points = min(max_points, len(scan_order))
    ordered_points = points[scan_order[:display_points]]
    
    # 绘制所有点（浅色背景）
    ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
              c='lightgray', s=8, alpha=0.3, label='All Points')
    
    # 绘制扫描路径
    colors = np.arange(display_points)
    scatter = ax.scatter(ordered_points[:, 0], ordered_points[:, 1], ordered_points[:, 2],
                       c=colors, s=50, cmap='plasma', alpha=0.9, label='Scan Path')
    
    # 绘制连接线
    ax.plot(ordered_points[:, 0], ordered_points[:, 1], ordered_points[:, 2],
           'r-', linewidth=2.5, alpha=0.8)
    
    # 标记起始点和结束点
    if len(ordered_points) > 0:
        ax.scatter(ordered_points[0, 0], ordered_points[0, 1], ordered_points[0, 2],
                  c='green', s=120, marker='o', label='Start', edgecolors='black', linewidth=2)
        if len(ordered_points) > 1:
            ax.scatter(ordered_points[-1, 0], ordered_points[-1, 1], ordered_points[-1, 2],
                      c='red', s=120, marker='s', label='End', edgecolors='black', linewidth=2)
    
    # 设置标题和标签
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('X', fontsize=14)
    ax.set_ylabel('Y', fontsize=14)
    ax.set_zlabel('Z', fontsize=14)
    
    # 设置视角
    ax.view_init(elev=25, azim=45)
    
    # 设置坐标轴比例
    max_range = np.array([points[:, 0].max() - points[:, 0].min(),
                         points[:, 1].max() - points[:, 1].min(),
                         points[:, 2].max() - points[:, 2].min()]).max() / 2.0
    mid_x = (points[:, 0].max() + points[:, 0].min()) * 0.5
    mid_y = (points[:, 1].max() + points[:, 1].min()) * 0.5
    mid_z = (points[:, 2].max() + points[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    # 添加图例和颜色条
    ax.legend(loc='upper left', bbox_to_anchor=(0, 1), fontsize=12)
    cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, aspect=30)
    cbar.set_label('Scan Order', fontsize=14)
    
    # 美化图片
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"图片已保存: {save_path}")
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='快速生成点云扫描可视化图片')
    parser.add_argument('--sample_idx', type=int, default=0, help='样本索引')
    parser.add_argument('--max_points', type=int, default=120, help='显示的最大点数')
    parser.add_argument('--output_dir', type=str, default='quick_images', help='输出目录')
    
    args = parser.parse_args()
    
    print(f"快速生成样本 {args.sample_idx} 的可视化图片")
    print("=" * 50)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    points, label = load_sample_data(sample_idx=args.sample_idx)
    if points is None:
        return
    
    # 标准化并采样
    points = normalize_points(points)
    points = points[:args.max_points * 2]  # 取更多点以便选择
    
    print(f"样本标签: {label}, 使用点数: {len(points)}")
    
    # 获取扫描顺序
    hilbert_order, hilbert_trans_order, deform_order = get_scanning_orders(points)
    
    # 生成图片
    create_single_scanning_image(
        points, hilbert_order,
        f"Hilbert Curve Scanning (Sample {args.sample_idx}, Label: {label})",
        os.path.join(args.output_dir, f"hilbert_sample_{args.sample_idx}.png"),
        args.max_points
    )
    
    create_single_scanning_image(
        points, hilbert_trans_order,
        f"Hilbert-Trans Scanning (Sample {args.sample_idx}, Label: {label})",
        os.path.join(args.output_dir, f"hilbert_trans_sample_{args.sample_idx}.png"),
        args.max_points
    )
    
    create_single_scanning_image(
        points, deform_order,
        f"Deformable Scanning (Sample {args.sample_idx}, Label: {label})",
        os.path.join(args.output_dir, f"deformable_sample_{args.sample_idx}.png"),
        args.max_points
    )
    
    print(f"\n样本 {args.sample_idx} 的图片生成完成！")
    print(f"输出目录: {args.output_dir}")

if __name__ == "__main__":
    main()
