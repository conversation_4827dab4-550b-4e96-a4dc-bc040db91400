#!/usr/bin/env python3
"""
Point Deformable Mamba 图片生成脚本
生成静态PNG图片，更直观地展示扫描过程
"""

import os
import sys
import numpy as np
import torch
import h5py
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 尝试导入模型相关模块
try:
    from models.point_mamba_scan import Group, Encoder, serialization_func
    from models.point_deformable_mamba import DeformableScanning
    MODELS_AVAILABLE = True
    print("成功导入模型模块")
except ImportError as e:
    print(f"无法导入模型模块: {e}")
    print("将使用简化的演示功能")
    MODELS_AVAILABLE = False

class PointCloudImageGenerator:
    """点云图片生成器"""
    
    def __init__(self, data_path="data/ScanObjectNN/main_split_nobg"):
        self.data_path = data_path
        # 设置matplotlib的样式
        plt.style.use('default')
        
    def load_scanobjectnn_data(self, subset="test", num_samples=3):
        """从ScanObjectNN数据集加载点云数据"""
        if subset == "test":
            h5_file = os.path.join(self.data_path, "test_objectdataset.h5")
        else:
            h5_file = os.path.join(self.data_path, "training_objectdataset.h5")
            
        if not os.path.exists(h5_file):
            print(f"数据文件不存在: {h5_file}")
            return None, None
            
        with h5py.File(h5_file, 'r') as f:
            points = np.array(f['data'][:num_samples]).astype(np.float32)
            labels = np.array(f['label'][:num_samples]).astype(int)
            
        print(f"成功加载 {len(points)} 个点云样本，每个样本有 {points.shape[1]} 个点")
        return points, labels
        
    def normalize_point_cloud(self, points):
        """标准化点云"""
        centroid = np.mean(points, axis=0)
        points = points - centroid
        max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
        points = points / max_dist
        return points
        
    def simple_hilbert_order(self, points, grid_size=0.02):
        """简化的希尔伯特排序"""
        scaled_coord = points / grid_size
        grid_coord = np.floor(scaled_coord).astype(np.int64)
        
        def z_order_key(coord):
            x, y, z = coord
            return (x & 0xFF) | ((y & 0xFF) << 8) | ((z & 0xFF) << 16)
        
        z_values = [z_order_key(coord) for coord in grid_coord]
        return np.argsort(z_values)
    
    def apply_real_hilbert_scanning(self, points):
        """应用真实的希尔伯特曲线扫描"""
        if not MODELS_AVAILABLE:
            return self.simple_hilbert_order(points), self.simple_hilbert_order(points)[::-1]
            
        try:
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            center_points = points_tensor
            features = torch.randn(1, points.shape[0], 192)
            pos_embed = torch.randn(1, points.shape[0], 192)
            
            # 希尔伯特扫描
            _, hilbert_order, _, _, _ = serialization_func(
                center_points, features, pos_embed, 'hilbert'
            )
            
            # 反希尔伯特扫描
            _, hilbert_trans_order, _, _, _ = serialization_func(
                center_points, features, pos_embed, 'hilbert-trans'
            )
            
            return hilbert_order.squeeze(0).numpy(), hilbert_trans_order.squeeze(0).numpy()
            
        except Exception as e:
            print(f"使用真实模型时出错: {e}")
            return self.simple_hilbert_order(points), self.simple_hilbert_order(points)[::-1]
    
    def apply_deformable_scanning(self, points):
        """应用可变形扫描"""
        if not MODELS_AVAILABLE:
            indices = np.arange(len(points))
            np.random.shuffle(indices)
            return indices
            
        try:
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            features = torch.randn(1, points.shape[0], 192)
            
            deformable_scanner = DeformableScanning(feature_dim=192, hidden_dim=96, k=4)
            
            with torch.no_grad():
                _, deform_indices = deformable_scanner(points_tensor, features)
                
            return deform_indices.squeeze(0).numpy()
            
        except Exception as e:
            print(f"使用可变形扫描时出错: {e}")
            indices = np.arange(len(points))
            np.random.shuffle(indices)
            return indices

    def create_3d_image(self, points, title="Point Cloud", colors=None, size=20, 
                       save_path=None, figsize=(10, 8), elev=20, azim=45):
        """创建3D点云图片"""
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        if colors is None:
            colors = 'blue'
        
        # 绘制点云
        scatter = ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                           c=colors, s=size, alpha=0.8, cmap='viridis')
        
        # 设置标题和标签
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('X', fontsize=12)
        ax.set_ylabel('Y', fontsize=12)
        ax.set_zlabel('Z', fontsize=12)
        
        # 设置视角
        ax.view_init(elev=elev, azim=azim)
        
        # 设置坐标轴比例
        max_range = np.array([points[:, 0].max() - points[:, 0].min(),
                             points[:, 1].max() - points[:, 1].min(),
                             points[:, 2].max() - points[:, 2].min()]).max() / 2.0
        mid_x = (points[:, 0].max() + points[:, 0].min()) * 0.5
        mid_y = (points[:, 1].max() + points[:, 1].min()) * 0.5
        mid_z = (points[:, 2].max() + points[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # 添加颜色条（如果使用了颜色映射）
        if isinstance(colors, (list, np.ndarray)) and len(colors) > 1:
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
            cbar.set_label('Scan Order', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图片已保存: {save_path}")
        
        plt.close()
        return fig

    def create_scanning_path_image(self, points, scan_order, title="Scanning Path", 
                                 save_path=None, figsize=(12, 10), max_points=150):
        """创建扫描路径图片"""
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # 限制显示点数以提高清晰度
        display_points = min(max_points, len(scan_order))
        ordered_points = points[scan_order[:display_points]]
        
        # 绘制所有点（浅色）
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                  c='lightblue', s=15, alpha=0.3, label='All Points')
        
        # 绘制扫描路径
        colors = np.arange(display_points)
        scatter = ax.scatter(ordered_points[:, 0], ordered_points[:, 1], ordered_points[:, 2],
                           c=colors, s=40, cmap='plasma', alpha=0.9, label='Scan Path')
        
        # 绘制连接线
        ax.plot(ordered_points[:, 0], ordered_points[:, 1], ordered_points[:, 2],
               'r-', linewidth=2, alpha=0.7)
        
        # 标记起始点和结束点
        if len(ordered_points) > 0:
            ax.scatter(ordered_points[0, 0], ordered_points[0, 1], ordered_points[0, 2],
                      c='green', s=100, marker='o', label='Start', edgecolors='black')
            if len(ordered_points) > 1:
                ax.scatter(ordered_points[-1, 0], ordered_points[-1, 1], ordered_points[-1, 2],
                          c='red', s=100, marker='s', label='End', edgecolors='black')
        
        # 设置标题和标签
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('X', fontsize=12)
        ax.set_ylabel('Y', fontsize=12)
        ax.set_zlabel('Z', fontsize=12)
        
        # 设置视角
        ax.view_init(elev=20, azim=45)
        
        # 设置坐标轴比例
        max_range = np.array([points[:, 0].max() - points[:, 0].min(),
                             points[:, 1].max() - points[:, 1].min(),
                             points[:, 2].max() - points[:, 2].min()]).max() / 2.0
        mid_x = (points[:, 0].max() + points[:, 0].min()) * 0.5
        mid_y = (points[:, 1].max() + points[:, 1].min()) * 0.5
        mid_z = (points[:, 2].max() + points[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # 添加图例和颜色条
        ax.legend(loc='upper left', bbox_to_anchor=(0, 1))
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
        cbar.set_label('Scan Order', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图片已保存: {save_path}")
        
        plt.close()
        return fig

    def create_comparison_image(self, points, hilbert_order, hilbert_trans_order, deform_order,
                              save_path=None, figsize=(20, 15), max_points=100):
        """创建对比图片"""
        fig = plt.figure(figsize=figsize)

        # 限制显示点数
        display_points = min(max_points, len(points))

        # 子图1: 原始点云
        ax1 = fig.add_subplot(2, 2, 1, projection='3d')
        ax1.scatter(points[:display_points, 0], points[:display_points, 1], points[:display_points, 2],
                   c='blue', s=30, alpha=0.8)
        ax1.set_title('Original Point Cloud', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        ax1.set_zlabel('Z')

        # 子图2: 希尔伯特扫描
        ax2 = fig.add_subplot(2, 2, 2, projection='3d')
        hilbert_points = points[hilbert_order[:display_points]]
        colors = np.arange(display_points)
        scatter2 = ax2.scatter(hilbert_points[:, 0], hilbert_points[:, 1], hilbert_points[:, 2],
                              c=colors, s=30, cmap='viridis', alpha=0.8)
        ax2.plot(hilbert_points[:, 0], hilbert_points[:, 1], hilbert_points[:, 2],
                'r-', linewidth=1.5, alpha=0.7)
        ax2.set_title('Hilbert Curve Scanning', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X')
        ax2.set_ylabel('Y')
        ax2.set_zlabel('Z')

        # 子图3: 反希尔伯特扫描
        ax3 = fig.add_subplot(2, 2, 3, projection='3d')
        hilbert_trans_points = points[hilbert_trans_order[:display_points]]
        scatter3 = ax3.scatter(hilbert_trans_points[:, 0], hilbert_trans_points[:, 1], hilbert_trans_points[:, 2],
                              c=colors, s=30, cmap='plasma', alpha=0.8)
        ax3.plot(hilbert_trans_points[:, 0], hilbert_trans_points[:, 1], hilbert_trans_points[:, 2],
                'g-', linewidth=1.5, alpha=0.7)
        ax3.set_title('Hilbert-Trans Scanning', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X')
        ax3.set_ylabel('Y')
        ax3.set_zlabel('Z')

        # 子图4: 可变形扫描
        ax4 = fig.add_subplot(2, 2, 4, projection='3d')
        deform_points = points[deform_order[:display_points]]
        scatter4 = ax4.scatter(deform_points[:, 0], deform_points[:, 1], deform_points[:, 2],
                              c=colors, s=30, cmap='cividis', alpha=0.8)
        ax4.plot(deform_points[:, 0], deform_points[:, 1], deform_points[:, 2],
                'purple', linewidth=1.5, alpha=0.7)
        ax4.set_title('Deformable Scanning', fontsize=14, fontweight='bold')
        ax4.set_xlabel('X')
        ax4.set_ylabel('Y')
        ax4.set_zlabel('Z')

        # 设置所有子图的视角
        for ax in [ax1, ax2, ax3, ax4]:
            ax.view_init(elev=20, azim=45)

            # 设置坐标轴比例
            max_range = np.array([points[:, 0].max() - points[:, 0].min(),
                                 points[:, 1].max() - points[:, 1].min(),
                                 points[:, 2].max() - points[:, 2].min()]).max() / 2.0
            mid_x = (points[:, 0].max() + points[:, 0].min()) * 0.5
            mid_y = (points[:, 1].max() + points[:, 1].min()) * 0.5
            mid_z = (points[:, 2].max() + points[:, 2].min()) * 0.5

            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)

        # 添加总标题
        fig.suptitle('Point Cloud Scanning Methods Comparison', fontsize=16, fontweight='bold')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"对比图片已保存: {save_path}")

        plt.close()
        return fig

def main():
    parser = argparse.ArgumentParser(description='Point Deformable Mamba 图片生成')
    parser.add_argument('--data_path', type=str, default='data/ScanObjectNN/main_split_nobg',
                        help='ScanObjectNN数据集路径')
    parser.add_argument('--num_samples', type=int, default=2,
                        help='要可视化的样本数量')
    parser.add_argument('--output_dir', type=str, default='images_output',
                        help='输出目录')
    parser.add_argument('--max_points', type=int, default=200,
                        help='每个样本使用的最大点数')

    args = parser.parse_args()

    print("Point Deformable Mamba 图片生成")
    print("=" * 50)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建图片生成器
    generator = PointCloudImageGenerator(data_path=args.data_path)

    try:
        # 加载数据
        points_data, labels = generator.load_scanobjectnn_data(num_samples=args.num_samples)
        if points_data is None:
            return

        # 处理每个样本
        for sample_idx in range(args.num_samples):
            print(f"\n处理样本 {sample_idx + 1}/{args.num_samples}")

            points = points_data[sample_idx]
            label = labels[sample_idx]

            # 标准化点云
            points = generator.normalize_point_cloud(points)

            # 限制点数以提高可视化效果
            points = points[:args.max_points]

            print(f"样本 {sample_idx}, 标签: {label}, 使用 {len(points)} 个点")

            # 应用不同的扫描方法
            hilbert_order, hilbert_trans_order = generator.apply_real_hilbert_scanning(points)
            deform_order = generator.apply_deformable_scanning(points)

            # 生成图片
            # 1. 原始点云
            generator.create_3d_image(
                points,
                title=f"Sample {sample_idx} - Original Point Cloud (Label: {label})",
                save_path=os.path.join(args.output_dir, f"sample_{sample_idx}_original.png")
            )

            # 2. 希尔伯特扫描路径
            generator.create_scanning_path_image(
                points,
                hilbert_order,
                title=f"Sample {sample_idx} - Hilbert Curve Scanning",
                save_path=os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert.png")
            )

            # 3. 反希尔伯特扫描路径
            generator.create_scanning_path_image(
                points,
                hilbert_trans_order,
                title=f"Sample {sample_idx} - Hilbert-Trans Scanning",
                save_path=os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert_trans.png")
            )

            # 4. 可变形扫描路径
            generator.create_scanning_path_image(
                points,
                deform_order,
                title=f"Sample {sample_idx} - Deformable Scanning",
                save_path=os.path.join(args.output_dir, f"sample_{sample_idx}_deformable.png")
            )

            # 5. 对比图片
            generator.create_comparison_image(
                points,
                hilbert_order,
                hilbert_trans_order,
                deform_order,
                save_path=os.path.join(args.output_dir, f"sample_{sample_idx}_comparison.png")
            )

            print(f"样本 {sample_idx} 图片生成完成")

        # 创建README文件
        create_images_readme(args.output_dir, args.num_samples)

        print(f"\n所有图片生成完成！")
        print(f"请查看 {args.output_dir} 目录中的PNG文件")
        print(f"README文件: {args.output_dir}/README.md")

    except Exception as e:
        print(f"图片生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_images_readme(output_dir, num_samples):
    """创建图片说明README文件"""
    readme_content = f"""# Point Deformable Mamba 可视化图片

本目录包含了Point Deformable Mamba模型不同扫描方法的可视化图片。

## 图片说明

### 扫描方法对比
"""

    for sample_idx in range(num_samples):
        readme_content += f"""
### 样本 {sample_idx}

- `sample_{sample_idx}_original.png` - 原始点云
- `sample_{sample_idx}_hilbert.png` - 希尔伯特曲线扫描
- `sample_{sample_idx}_hilbert_trans.png` - 反希尔伯特扫描
- `sample_{sample_idx}_deformable.png` - 可变形扫描
- `sample_{sample_idx}_comparison.png` - 四种方法对比

"""

    readme_content += """
## 扫描方法说明

1. **希尔伯特曲线扫描**: 使用希尔伯特曲线对3D空间进行序列化，保持空间局部性
2. **反希尔伯特扫描**: 交换坐标轴后的希尔伯特曲线扫描，提供不同的遍历顺序
3. **可变形扫描**: 基于点云特征自适应调整的扫描顺序

## 图片特征

- **颜色编码**: 扫描路径中的颜色表示扫描顺序（从蓝/紫到黄/红）
- **连接线**: 红色线条显示扫描的连接路径
- **起始点**: 绿色圆点标记扫描起始位置
- **结束点**: 红色方块标记扫描结束位置
- **分辨率**: 所有图片均为300 DPI高分辨率

## 使用建议

这些图片可以用于：
- 学术论文和报告
- 教学演示
- 算法分析和比较
- 模型理解和解释
"""

    with open(os.path.join(output_dir, "README.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)

if __name__ == "__main__":
    main()
