#!/usr/bin/env python3
"""
简化的测试可视化脚本
"""

import os
import sys
import numpy as np
import torch
import h5py
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_scanobjectnn_sample():
    """加载一个ScanObjectNN样本"""
    data_path = "data/ScanObjectNN/main_split_nobg/test_objectdataset.h5"
    
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return None, None
        
    with h5py.File(data_path, 'r') as f:
        points = np.array(f['data'][0]).astype(np.float32)  # 取第一个样本
        label = np.array(f['label'][0]).astype(int)
        
    print(f"成功加载点云样本，形状: {points.shape}, 标签: {label}")
    return points, label

def normalize_point_cloud(points):
    """标准化点云"""
    # 中心化
    centroid = np.mean(points, axis=0)
    points = points - centroid
    
    # 缩放到单位球
    max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
    points = points / max_dist
    
    return points

def simple_hilbert_order(points, grid_size=0.02):
    """简化的希尔伯特排序（用于演示）"""
    # 将点云映射到网格
    scaled_coord = points / grid_size
    grid_coord = np.floor(scaled_coord).astype(np.int64)
    
    # 简单的Z-order排序作为希尔伯特的近似
    def z_order_key(coord):
        x, y, z = coord
        # 简单的Z-order编码
        return (x & 0xFF) | ((y & 0xFF) << 8) | ((z & 0xFF) << 16)
    
    # 计算每个点的Z-order值
    z_values = [z_order_key(coord) for coord in grid_coord]
    
    # 返回排序后的索引
    return np.argsort(z_values)

def create_3d_visualization(points, title="Point Cloud", colors=None, size=3):
    """创建3D可视化"""
    fig = go.Figure()
    
    if colors is None:
        colors = 'blue'
        
    fig.add_trace(go.Scatter3d(
        x=points[:, 0],
        y=points[:, 1], 
        z=points[:, 2],
        mode='markers',
        marker=dict(
            size=size,
            color=colors,
            colorscale='Viridis' if isinstance(colors, (list, np.ndarray)) else None,
            showscale=True if isinstance(colors, (list, np.ndarray)) else False
        ),
        name=title
    ))
    
    fig.update_layout(
        title=title,
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='cube'
        ),
        width=800,
        height=600
    )
    
    return fig

def create_scanning_path_visualization(points, scan_order, title="Scanning Path"):
    """创建扫描路径可视化"""
    fig = go.Figure()
    
    # 添加点云
    fig.add_trace(go.Scatter3d(
        x=points[:, 0],
        y=points[:, 1],
        z=points[:, 2],
        mode='markers',
        marker=dict(
            size=4,
            color='lightblue',
            opacity=0.6
        ),
        name='Points'
    ))
    
    # 添加扫描路径（只显示前100个点以避免过于复杂）
    max_points = min(100, len(scan_order))
    ordered_points = points[scan_order[:max_points]]
    
    fig.add_trace(go.Scatter3d(
        x=ordered_points[:, 0],
        y=ordered_points[:, 1],
        z=ordered_points[:, 2],
        mode='lines+markers',
        line=dict(
            color='red',
            width=3
        ),
        marker=dict(
            size=6,
            color=np.arange(max_points),
            colorscale='Plasma',
            showscale=True,
            colorbar=dict(title="Scan Order")
        ),
        name='Scan Path'
    ))
    
    fig.update_layout(
        title=title,
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='cube'
        ),
        width=900,
        height=700
    )
    
    return fig

def main():
    print("测试Point Cloud可视化")
    print("=" * 40)
    
    # 创建输出目录
    output_dir = "test_visualization_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    points, label = load_scanobjectnn_sample()
    if points is None:
        return
    
    # 标准化点云
    points = normalize_point_cloud(points)
    
    # 为了演示，我们只使用前500个点
    points = points[:500]
    
    print(f"使用 {len(points)} 个点进行可视化")
    
    # 创建原始点云可视化
    fig_original = create_3d_visualization(
        points, 
        title=f"Original Point Cloud (Label: {label})"
    )
    fig_original.write_html(os.path.join(output_dir, "original.html"))
    print("原始点云可视化已保存")
    
    # 创建简化的希尔伯特扫描
    hilbert_order = simple_hilbert_order(points)
    
    # 创建希尔伯特扫描路径可视化
    fig_hilbert = create_scanning_path_visualization(
        points,
        hilbert_order,
        title="Hilbert-like Scanning Path"
    )
    fig_hilbert.write_html(os.path.join(output_dir, "hilbert_scan.html"))
    print("希尔伯特扫描可视化已保存")
    
    # 创建反向扫描（简单反转顺序）
    reverse_order = hilbert_order[::-1]
    fig_reverse = create_scanning_path_visualization(
        points,
        reverse_order,
        title="Reverse Scanning Path"
    )
    fig_reverse.write_html(os.path.join(output_dir, "reverse_scan.html"))
    print("反向扫描可视化已保存")
    
    print(f"\n所有可视化文件已保存到 {output_dir} 目录")
    print("请打开HTML文件查看结果")

if __name__ == "__main__":
    main()
