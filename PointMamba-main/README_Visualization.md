# Point Deformable Mamba 可视化工具

这个工具用于可视化Point Deformable Mamba模型中不同扫描方法对点云数据的处理过程。

## 功能特性

- 🔍 **多种扫描方法可视化**：希尔伯特曲线、反希尔伯特曲线、可变形扫描
- 📊 **交互式3D可视化**：使用Plotly生成可交互的3D图表
- 🎨 **扫描路径动画**：通过颜色编码显示扫描顺序
- 📈 **对比分析**：并排比较不同扫描方法的效果
- 🌐 **Web界面**：生成HTML文件，可在浏览器中查看

## 安装依赖

确保已安装以下Python包：

```bash
pip install plotly h5py numpy torch
```

## 使用方法

### 1. 基本使用

```bash
# 运行演示可视化（推荐）
python demo_visualization.py --num_samples 3

# 运行完整可视化（需要完整的模型环境）
python visualize_deformable_mamba.py --num_samples 3
```

### 2. 参数说明

- `--data_path`: ScanObjectNN数据集路径（默认：`data/ScanObjectNN/main_split_nobg`）
- `--num_samples`: 要可视化的样本数量（默认：2）
- `--output_dir`: 输出目录（默认：`demo_visualization_output`）

### 3. 输出文件

运行后会在输出目录生成以下文件：

- `summary.html`: 总结页面，包含所有可视化的链接
- `sample_X_original.html`: 原始点云可视化
- `sample_X_hilbert.html`: 希尔伯特曲线扫描可视化
- `sample_X_hilbert_trans.html`: 反希尔伯特扫描可视化
- `sample_X_deformable.html`: 可变形扫描可视化
- `sample_X_comparison.html`: 四种方法的对比可视化

## 扫描方法说明

### 1. 希尔伯特曲线扫描 (Hilbert Curve)
- 使用希尔伯特曲线对3D空间进行序列化
- 保持空间局部性，相邻的点在序列中也相对接近
- 适合处理具有空间连续性的点云数据

### 2. 反希尔伯特扫描 (Hilbert-Trans)
- 交换坐标轴后的希尔伯特曲线扫描
- 提供不同的空间遍历顺序
- 增加模型对不同空间方向的鲁棒性

### 3. 可变形扫描 (Deformable Scanning)
- 基于点云特征自适应调整的扫描顺序
- 能够根据数据特点动态优化扫描路径
- 提高模型对复杂几何结构的处理能力

## 可视化界面操作

在生成的HTML文件中，您可以：

- 🖱️ **拖拽旋转**：鼠标左键拖拽旋转3D视角
- 🔍 **缩放**：鼠标滚轮缩放视图
- 📱 **重置视角**：双击重置到默认视角
- 🎨 **颜色编码**：观察扫描路径的颜色变化，表示扫描顺序
- 📊 **图例**：查看颜色条了解扫描顺序的映射

## 示例输出

运行成功后，您将看到类似以下的输出：

```
Point Deformable Mamba 演示可视化
==================================================
成功加载 2 个点云样本，每个样本有 2048 个点

处理样本 1/2
样本 0, 标签: 0, 使用 300 个点
样本 0 可视化完成

处理样本 2/2
样本 1, 标签: 0, 使用 300 个点
样本 1 可视化完成

所有可视化完成！
请打开 demo_visualization_output/summary.html 查看结果
```

## 故障排除

### 1. 数据集路径问题
如果提示找不到数据文件，请确保：
- ScanObjectNN数据集已正确下载
- 数据路径参数正确设置

### 2. CUDA相关错误
如果遇到CUDA相关错误，脚本会自动回退到简化版本的功能，不影响基本可视化。

### 3. 内存不足
如果处理大量点云时内存不足，可以：
- 减少`--num_samples`参数
- 修改代码中的点云采样数量

## 技术细节

- **点云预处理**：自动进行中心化和归一化
- **性能优化**：限制显示点数以提高渲染性能
- **兼容性**：支持有无完整模型环境的运行
- **错误处理**：包含完善的异常处理机制

## 扩展功能

您可以基于这个工具进行扩展：

1. **添加新的扫描方法**：在`PointCloudVisualizer`类中添加新方法
2. **自定义可视化样式**：修改Plotly图表的样式参数
3. **批量处理**：扩展为处理整个数据集的批量可视化
4. **性能分析**：添加扫描方法的性能对比分析

## 联系信息

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**注意**：此工具主要用于研究和教学目的，帮助理解Point Deformable Mamba模型的工作原理。
