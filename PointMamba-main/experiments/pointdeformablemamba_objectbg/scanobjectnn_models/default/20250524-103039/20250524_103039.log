2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - Copy the Config file from cfgs/scanobjectnn_models/pointdeformablemamba_objectbg.yaml to ./experiments/pointdeformablemamba_objectbg/scanobjectnn_models/default/20250524-103039/config.yaml
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.config : cfgs/scanobjectnn_models/pointdeformablemamba_objectbg.yaml
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.launcher : none
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.local_rank : 0
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.num_workers : 8
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.seed : 0
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.deterministic : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.sync_bn : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.exp_name : default
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.loss : cd1
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.start_ckpts : None
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.ckpts : None
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.val_freq : 1
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.vote : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.resume : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.test : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.finetune_model : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.scratch_model : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.mode : None
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.way : -1
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.shot : -1
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.fold : -1
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.experiment_path : ./experiments/pointdeformablemamba_objectbg/scanobjectnn_models/default/20250524-103039
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.tfboard_path : ./experiments/pointdeformablemamba_objectbg/scanobjectnn_models/TFBoard/default/20250524-103039
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.log_name : pointdeformablemamba_objectbg
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.use_gpu : True
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - args.distributed : False
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.optimizer = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.optimizer.type : AdamW
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.optimizer.kwargs = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.optimizer.kwargs.lr : 0.0005
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.optimizer.kwargs.weight_decay : 0.05
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.scheduler = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.scheduler.type : CosLR
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.scheduler.kwargs = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.scheduler.kwargs.epochs : 300
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.scheduler.kwargs.initial_epochs : 10
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train._base_ = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train._base_.NAME : ScanObjectNN
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train._base_.ROOT : data/ScanObjectNN/main_split
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train.others = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train.others.subset : train
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.train.others.bs : 32
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val._base_ = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val._base_.NAME : ScanObjectNN
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val._base_.ROOT : data/ScanObjectNN/main_split
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val.others = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val.others.subset : test
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.val.others.bs : 32
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test._base_ = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test._base_.NAME : ScanObjectNN
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test._base_.ROOT : data/ScanObjectNN/main_split
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test.others = edict()
2025-05-24 10:30:39,549 - pointdeformablemamba_objectbg - INFO - config.dataset.test.others.subset : test
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.dataset.test.others.bs : 32
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model = edict()
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.NAME : PointDeformableMamba
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.trans_dim : 384
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.depth : 12
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.drop_path : 0.1
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.cls_dim : 15
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.num_group : 512
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.group_size : 32
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.model.encoder_dims : 384
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.total_bs : 32
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.step_per_update : 1
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.max_epoch : 300
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - config.grad_norm_clip : 10
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - Distributed training: False
2025-05-24 10:30:39,550 - pointdeformablemamba_objectbg - INFO - Set random seed to 0, deterministic: False
2025-05-24 10:30:40,097 - pointdeformablemamba_objectbg - INFO - Model: PointDeformableMamba(
  (group_divider): Group(
    (knn): KNN()
  )
  (encoder): Encoder(
    (first_conv): Sequential(
      (0): Conv1d(3, 128, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU(inplace=True)
      (3): Conv1d(128, 256, kernel_size=(1,), stride=(1,))
    )
    (second_conv): Sequential(
      (0): Conv1d(512, 512, kernel_size=(1,), stride=(1,))
      (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU(inplace=True)
      (3): Conv1d(512, 384, kernel_size=(1,), stride=(1,))
    )
  )
  (pos_embed): Sequential(
    (0): Linear(in_features=3, out_features=128, bias=True)
    (1): GELU(approximate='none')
    (2): Linear(in_features=128, out_features=384, bias=True)
  )
  (deformable_scanning): DeformableScanning(
    (offset_network): Point3DOffsetNetwork(
      (stages): ModuleList(
        (0): Sequential(
          (0): Conv1d(384, 192, kernel_size=(7,), stride=(1,), padding=(3,), groups=48)
          (1): GELU(approximate='none')
          (2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        )
        (1): Sequential(
          (0): Conv1d(192, 192, kernel_size=(5,), stride=(1,), padding=(2,), groups=48)
          (1): GELU(approximate='none')
          (2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        )
        (2): Sequential(
          (0): Conv1d(192, 192, kernel_size=(3,), stride=(1,), padding=(1,), groups=48)
          (1): GELU(approximate='none')
          (2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        )
      )
      (channel_attention): ChannelAttention(
        (avg_pool): AdaptiveAvgPool1d(output_size=1)
        (max_pool): AdaptiveMaxPool1d(output_size=1)
        (fc): Sequential(
          (0): Conv1d(192, 12, kernel_size=(1,), stride=(1,), bias=False)
          (1): ReLU()
          (2): Conv1d(12, 192, kernel_size=(1,), stride=(1,), bias=False)
        )
        (sigmoid): Sigmoid()
      )
      (offset_predictor): Conv1d(192, 4, kernel_size=(1,), stride=(1,), bias=False)
    )
    (point_aggregation): DeformablePointAggregation(
      (feature_transform): Sequential(
        (0): Linear(in_features=384, out_features=384, bias=True)
        (1): ReLU()
        (2): Linear(in_features=384, out_features=384, bias=True)
      )
      (knn): KNN()
    )
  )
  (enhanced_blocks): ModuleList(
    (0): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): Identity()
    )
    (1): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (2): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (3): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (4): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (5): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (6): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (7): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (8): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (9): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (10): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
    (11): DeformableMambaBlock(
      (forward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (backward_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (deformable_mamba): Mamba(
        (in_proj): Linear(in_features=384, out_features=1536, bias=False)
        (conv1d): Conv1d(768, 768, kernel_size=(4,), stride=(1,), padding=(3,), groups=768)
        (act): SiLU()
        (x_proj): Linear(in_features=768, out_features=56, bias=False)
        (dt_proj): Linear(in_features=24, out_features=768, bias=True)
        (out_proj): Linear(in_features=768, out_features=384, bias=False)
      )
      (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (norm3): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
      (fusion): Sequential(
        (0): Linear(in_features=1152, out_features=768, bias=True)
        (1): GELU(approximate='none')
        (2): Linear(in_features=768, out_features=384, bias=True)
      )
      (drop_path): DropPath()
    )
  )
  (cls_head_finetune): Sequential(
    (0): Linear(in_features=384, out_features=256, bias=True)
    (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU(inplace=True)
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=256, out_features=256, bias=True)
    (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU(inplace=True)
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=256, out_features=15, bias=True)
  )
  (loss_ce): CrossEntropyLoss()
)
2025-05-24 10:30:40,657 - pointdeformablemamba_objectbg - INFO - Using Data parallel ...
