# Point Deformable Mamba 可视化图片

本目录包含了Point Deformable Mamba模型不同扫描方法的可视化图片。

## 图片说明

### 扫描方法对比

### 样本 0

- `sample_0_original.png` - 原始点云
- `sample_0_hilbert.png` - 希尔伯特曲线扫描
- `sample_0_hilbert_trans.png` - 反希尔伯特扫描
- `sample_0_deformable.png` - 可变形扫描
- `sample_0_comparison.png` - 四种方法对比


### 样本 1

- `sample_1_original.png` - 原始点云
- `sample_1_hilbert.png` - 希尔伯特曲线扫描
- `sample_1_hilbert_trans.png` - 反希尔伯特扫描
- `sample_1_deformable.png` - 可变形扫描
- `sample_1_comparison.png` - 四种方法对比


## 扫描方法说明

1. **希尔伯特曲线扫描**: 使用希尔伯特曲线对3D空间进行序列化，保持空间局部性
2. **反希尔伯特扫描**: 交换坐标轴后的希尔伯特曲线扫描，提供不同的遍历顺序
3. **可变形扫描**: 基于点云特征自适应调整的扫描顺序

## 图片特征

- **颜色编码**: 扫描路径中的颜色表示扫描顺序（从蓝/紫到黄/红）
- **连接线**: 红色线条显示扫描的连接路径
- **起始点**: 绿色圆点标记扫描起始位置
- **结束点**: 红色方块标记扫描结束位置
- **分辨率**: 所有图片均为300 DPI高分辨率

## 使用建议

这些图片可以用于：
- 学术论文和报告
- 教学演示
- 算法分析和比较
- 模型理解和解释
