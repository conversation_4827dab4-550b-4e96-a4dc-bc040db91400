# Point Deformable Mamba 模型流程信息

## 样本信息
- **样本索引**: 2
- **样本标签**: 0
- **数据集**: ScanObjectNN

## 处理流程

### 1. 原始点云
- **点数**: 2048
- **来源**: ScanObjectNN数据集
- **预处理**: 中心化和归一化

### 2. FPS采样
- **采样后点数**: 1024
- **采样方法**: Farthest Point Sampling (FPS)
- **目的**: 保持点云几何特征的同时减少计算量

### 3. 模型处理
- **分组数量**: 256
- **分组方法**: PointDeformableMamba的Group模块
- **特征编码**: Encoder模块提取局部特征

### 4. 扫描方法
- **希尔伯特扫描**: 使用3D希尔伯特曲线序列化分组中心点
- **反希尔伯特扫描**: 交换坐标轴后的希尔伯特扫描
- **可变形扫描**: 基于点云特征的自适应扫描顺序

## 可视化说明

### Web可视化 (HTML)
- 交互式3D可视化
- 支持旋转、缩放、重置
- 清晰显示每个处理步骤

### 静态图片 (PNG)
- 高分辨率300 DPI
- 六个子图展示完整流程
- 适合学术论文和报告

## 技术细节

### 模型配置
- **Transformer维度**: 192
- **深度**: 8
- **分组大小**: 32
- **编码器维度**: 192

### 扫描特点
- **颜色编码**: 扫描顺序用颜色渐变表示
- **路径连线**: 红色线条显示扫描路径
- **空间局部性**: 希尔伯特曲线保持空间邻近关系

---
生成时间: 2025-06-18 21:02:40
