optimizer:
  type: AdamW
  kwargs:
    lr: 0.0005
    weight_decay: 0.05

scheduler:
  type: CosLR
  kwargs:
    epochs: 300
    initial_epochs: 10

dataset:
  train:
    _base_: cfgs/dataset_configs/ScanObjectNN_objectbg.yaml
    others:
      subset: train
  val:
    _base_: cfgs/dataset_configs/ScanObjectNN_objectbg.yaml
    others:
      subset: test
  test:
    _base_: cfgs/dataset_configs/ScanObjectNN_objectbg.yaml
    others:
      subset: test

model:
  NAME: PointDeformableMamba
  trans_dim: 192         # 降低transformer维度
  depth: 8              # 减少层数
  drop_path: 0.1
  cls_dim: 15
  num_group: 256        # 减少分组数量
  group_size: 32
  encoder_dims: 192     # 降低编码器维度

npoints: 1024
total_bs: 32
step_per_update: 1
max_epoch: 300
grad_norm_clip: 10 