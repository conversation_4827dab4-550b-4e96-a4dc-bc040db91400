optimizer:
  type: AdamW
  kwargs:
    lr: 0.0005
    weight_decay: 0.05

scheduler:
  type: CosLR
  kwargs:
    epochs: 300
    initial_epochs: 10

dataset:
  train:
    _base_: cfgs/dataset_configs/ScanObjectNN_hardest.yaml
    others:
      subset: train
  val:
    _base_: cfgs/dataset_configs/ScanObjectNN_hardest.yaml
    others:
      subset: test
  test:
    _base_: cfgs/dataset_configs/ScanObjectNN_hardest.yaml
    others:
      subset: test

model:
  NAME: PointDeformableMamba
  trans_dim: 384
  depth: 12
  drop_path: 0.1
  cls_dim: 15
  num_group: 512
  group_size: 32
  encoder_dims: 384

total_bs: 32
step_per_update: 1
max_epoch: 300
grad_norm_clip: 10 