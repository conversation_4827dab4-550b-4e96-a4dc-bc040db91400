#!/usr/bin/env python3
"""
Point Deformable Mamba 可视化脚本
展示DS扫描、希尔伯特曲线和反希尔伯特曲线对点云的扫描过程
"""

import os
import sys
import numpy as np
import torch
import h5py
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import argparse
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型相关模块
try:
    from models.point_deformable_mamba import PointDeformableMamba, DeformableScanning
    from models.point_mamba_scan import Group, Encoder, serialization_func
    from models.serialization import Point, encode
    from models.hilbert import encode as hilbert_encode
    from datasets.ScanObjectNNDataset import ScanObjectNN
    from utils.misc import fps
except ImportError as e:
    print(f"导入模块时出错: {e}")
    print("将使用简化版本的功能")

class PointCloudVisualizer:
    """点云可视化器"""
    
    def __init__(self, data_path="data/ScanObjectNN/main_split_nobg", device="cuda"):
        self.data_path = data_path
        self.device = device
        self.setup_model()
        
    def setup_model(self):
        """设置模型配置"""
        # 创建简化的模型配置
        class Config:
            def __init__(self):
                self.trans_dim = 192
                self.depth = 8
                self.cls_dim = 15
                self.num_group = 256
                self.group_size = 32
                self.encoder_dims = 192
                self.drop_path = 0.1
                self.use_cls_token = False
                self.max_head = False
                self.avg_head = True
        
        self.config = Config()
        
    def load_scanobjectnn_data(self, subset="test", num_samples=5):
        """从ScanObjectNN数据集加载点云数据"""
        if subset == "test":
            h5_file = os.path.join(self.data_path, "test_objectdataset.h5")
        else:
            h5_file = os.path.join(self.data_path, "training_objectdataset.h5")
            
        if not os.path.exists(h5_file):
            print(f"数据文件不存在: {h5_file}")
            print("请确保已下载ScanObjectNN数据集")
            return None, None
            
        with h5py.File(h5_file, 'r') as f:
            points = np.array(f['data'][:num_samples]).astype(np.float32)
            labels = np.array(f['label'][:num_samples]).astype(int)
            
        print(f"成功加载 {len(points)} 个点云样本，每个样本有 {points.shape[1]} 个点")
        return points, labels
        
    def normalize_point_cloud(self, points):
        """标准化点云"""
        # 中心化
        centroid = np.mean(points, axis=0)
        points = points - centroid
        
        # 缩放到单位球
        max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
        points = points / max_dist
        
        return points
        
    def group_points(self, points, num_group=256, group_size=32):
        """将点云分组"""
        points_tensor = torch.from_numpy(points).float().unsqueeze(0)  # (1, N, 3)
        
        # 使用FPS进行采样
        if points_tensor.shape[1] > num_group * group_size:
            points_tensor = fps(points_tensor, num_group * group_size)
        
        # 创建分组器
        group_divider = Group(num_group=num_group, group_size=group_size)
        
        # 分组
        neighborhood, center = group_divider(points_tensor)
        
        return neighborhood.squeeze(0).numpy(), center.squeeze(0).numpy()
        
    def apply_hilbert_scanning(self, center_points, features=None):
        """应用希尔伯特曲线扫描"""
        center_tensor = torch.from_numpy(center_points).float().unsqueeze(0)
        
        if features is None:
            features = torch.randn(1, center_points.shape[0], 192)
        else:
            features = torch.from_numpy(features).float().unsqueeze(0)
            
        pos_embed = torch.randn(1, center_points.shape[0], 192)
        
        # 希尔伯特扫描
        _, hilbert_order, _, hilbert_features, _ = serialization_func(
            center_tensor, features, pos_embed, 'hilbert'
        )
        
        # 反希尔伯特扫描
        _, hilbert_trans_order, _, hilbert_trans_features, _ = serialization_func(
            center_tensor, features, pos_embed, 'hilbert-trans'
        )
        
        return (hilbert_order.squeeze(0).numpy(), 
                hilbert_trans_order.squeeze(0).numpy(),
                hilbert_features.squeeze(0).numpy(),
                hilbert_trans_features.squeeze(0).numpy())
        
    def apply_deformable_scanning(self, center_points, features):
        """应用可变形扫描"""
        from models.point_deformable_mamba import DeformableScanning
        
        center_tensor = torch.from_numpy(center_points).float().unsqueeze(0)
        features_tensor = torch.from_numpy(features).float().unsqueeze(0)
        
        # 创建可变形扫描模块
        deformable_scanner = DeformableScanning(
            feature_dim=features.shape[-1], 
            hidden_dim=96, 
            k=4
        )
        
        # 应用可变形扫描
        with torch.no_grad():
            deformed_features, deform_indices = deformable_scanner(
                center_tensor, features_tensor
            )
            
        return (deform_indices.squeeze(0).numpy(), 
                deformed_features.squeeze(0).numpy())

    def create_3d_visualization(self, points, title="Point Cloud", colors=None, size=3):
        """创建3D可视化"""
        fig = go.Figure()
        
        if colors is None:
            colors = 'blue'
            
        fig.add_trace(go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1], 
            z=points[:, 2],
            mode='markers',
            marker=dict(
                size=size,
                color=colors,
                colorscale='Viridis' if isinstance(colors, (list, np.ndarray)) else None,
                showscale=True if isinstance(colors, (list, np.ndarray)) else False
            ),
            name=title
        ))
        
        fig.update_layout(
            title=title,
            scene=dict(
                xaxis_title='X',
                yaxis_title='Y',
                zaxis_title='Z',
                aspectmode='cube'
            ),
            width=800,
            height=600
        )
        
        return fig

    def create_scanning_path_visualization(self, points, scan_order, title="Scanning Path"):
        """创建扫描路径可视化"""
        fig = go.Figure()

        # 添加点云
        fig.add_trace(go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1],
            z=points[:, 2],
            mode='markers',
            marker=dict(
                size=4,
                color='lightblue',
                opacity=0.6
            ),
            name='Points'
        ))

        # 添加扫描路径
        ordered_points = points[scan_order]
        fig.add_trace(go.Scatter3d(
            x=ordered_points[:, 0],
            y=ordered_points[:, 1],
            z=ordered_points[:, 2],
            mode='lines+markers',
            line=dict(
                color='red',
                width=3
            ),
            marker=dict(
                size=6,
                color=np.arange(len(scan_order)),
                colorscale='Plasma',
                showscale=True,
                colorbar=dict(title="Scan Order")
            ),
            name='Scan Path'
        ))

        fig.update_layout(
            title=title,
            scene=dict(
                xaxis_title='X',
                yaxis_title='Y',
                zaxis_title='Z',
                aspectmode='cube'
            ),
            width=900,
            height=700
        )

        return fig

    def create_comparison_visualization(self, points, hilbert_order, hilbert_trans_order, deform_order):
        """创建不同扫描方法的对比可视化"""
        fig = make_subplots(
            rows=2, cols=2,
            specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}],
                   [{'type': 'scatter3d'}, {'type': 'scatter3d'}]],
            subplot_titles=('Original Point Cloud', 'Hilbert Curve Scanning',
                          'Hilbert-Trans Scanning', 'Deformable Scanning'),
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )

        # 原始点云
        fig.add_trace(go.Scatter3d(
            x=points[:, 0], y=points[:, 1], z=points[:, 2],
            mode='markers',
            marker=dict(size=4, color='blue'),
            name='Original'
        ), row=1, col=1)

        # 希尔伯特扫描
        hilbert_points = points[hilbert_order]
        fig.add_trace(go.Scatter3d(
            x=hilbert_points[:, 0], y=hilbert_points[:, 1], z=hilbert_points[:, 2],
            mode='lines+markers',
            line=dict(color='red', width=2),
            marker=dict(size=3, color=np.arange(len(hilbert_order)), colorscale='Viridis'),
            name='Hilbert'
        ), row=1, col=2)

        # 反希尔伯特扫描
        hilbert_trans_points = points[hilbert_trans_order]
        fig.add_trace(go.Scatter3d(
            x=hilbert_trans_points[:, 0], y=hilbert_trans_points[:, 1], z=hilbert_trans_points[:, 2],
            mode='lines+markers',
            line=dict(color='green', width=2),
            marker=dict(size=3, color=np.arange(len(hilbert_trans_order)), colorscale='Plasma'),
            name='Hilbert-Trans'
        ), row=2, col=1)

        # 可变形扫描
        deform_points = points[deform_order]
        fig.add_trace(go.Scatter3d(
            x=deform_points[:, 0], y=deform_points[:, 1], z=deform_points[:, 2],
            mode='lines+markers',
            line=dict(color='purple', width=2),
            marker=dict(size=3, color=np.arange(len(deform_order)), colorscale='Cividis'),
            name='Deformable'
        ), row=2, col=2)

        fig.update_layout(
            title="Point Cloud Scanning Methods Comparison",
            height=800,
            width=1200,
            showlegend=False
        )

        return fig

    def visualize_sample(self, sample_idx=0):
        """可视化单个样本的扫描过程"""
        # 加载数据
        points_data, labels = self.load_scanobjectnn_data(num_samples=sample_idx+1)
        if points_data is None:
            return

        points = points_data[sample_idx]
        label = labels[sample_idx]

        print(f"可视化样本 {sample_idx}, 标签: {label}")

        # 标准化点云
        points = self.normalize_point_cloud(points)

        # 分组
        neighborhood, center_points = self.group_points(points,
                                                       num_group=self.config.num_group,
                                                       group_size=self.config.group_size)

        print(f"分组后得到 {len(center_points)} 个中心点")

        # 创建特征编码器
        encoder = Encoder(encoder_channel=self.config.encoder_dims)
        with torch.no_grad():
            neighborhood_tensor = torch.from_numpy(neighborhood).float().unsqueeze(0)
            group_features = encoder(neighborhood_tensor).squeeze(0).numpy()

        # 应用不同的扫描方法
        hilbert_order, hilbert_trans_order, hilbert_features, hilbert_trans_features = \
            self.apply_hilbert_scanning(center_points, group_features)

        deform_order, deformed_features = self.apply_deformable_scanning(center_points, group_features)

        return {
            'points': points,
            'center_points': center_points,
            'hilbert_order': hilbert_order,
            'hilbert_trans_order': hilbert_trans_order,
            'deform_order': deform_order,
            'label': label
        }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Point Deformable Mamba 可视化')
    parser.add_argument('--data_path', type=str, default='data/ScanObjectNN/main_split_nobg',
                        help='ScanObjectNN数据集路径')
    parser.add_argument('--num_samples', type=int, default=3,
                        help='要可视化的样本数量')
    parser.add_argument('--output_dir', type=str, default='visualization_output',
                        help='输出目录')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建可视化器
    visualizer = PointCloudVisualizer(data_path=args.data_path)

    print("Point Deformable Mamba 可视化脚本")
    print("=" * 50)

    try:
        # 可视化多个样本
        for sample_idx in range(args.num_samples):
            print(f"\n处理样本 {sample_idx + 1}/{args.num_samples}")

            # 获取可视化数据
            vis_data = visualizer.visualize_sample(sample_idx)
            if vis_data is None:
                continue

            # 创建原始点云可视化
            fig_original = visualizer.create_3d_visualization(
                vis_data['points'],
                title=f"Sample {sample_idx} - Original Point Cloud (Label: {vis_data['label']})"
            )
            fig_original.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_original.html"))

            # 创建中心点可视化
            fig_centers = visualizer.create_3d_visualization(
                vis_data['center_points'],
                title=f"Sample {sample_idx} - Group Centers",
                colors='red',
                size=6
            )
            fig_centers.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_centers.html"))

            # 创建希尔伯特扫描路径可视化
            fig_hilbert = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['hilbert_order'],
                title=f"Sample {sample_idx} - Hilbert Curve Scanning"
            )
            fig_hilbert.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert.html"))

            # 创建反希尔伯特扫描路径可视化
            fig_hilbert_trans = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['hilbert_trans_order'],
                title=f"Sample {sample_idx} - Hilbert-Trans Scanning"
            )
            fig_hilbert_trans.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert_trans.html"))

            # 创建可变形扫描路径可视化
            fig_deformable = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['deform_order'],
                title=f"Sample {sample_idx} - Deformable Scanning"
            )
            fig_deformable.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_deformable.html"))

            # 创建对比可视化
            fig_comparison = visualizer.create_comparison_visualization(
                vis_data['center_points'],
                vis_data['hilbert_order'],
                vis_data['hilbert_trans_order'],
                vis_data['deform_order']
            )
            fig_comparison.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_comparison.html"))

            print(f"样本 {sample_idx} 可视化完成，文件保存在 {args.output_dir}")

        # 创建总结HTML文件
        create_summary_html(args.output_dir, args.num_samples)

        print(f"\n所有可视化完成！")
        print(f"请打开 {args.output_dir}/summary.html 查看结果")

    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_summary_html(output_dir, num_samples):
    """创建总结HTML文件"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 可视化结果</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .sample-section { margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; }
            .visualization-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .viz-item { text-align: center; }
            .viz-item a { display: block; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 5px; }
            .viz-item a:hover { background: #e0e0e0; }
            h1, h2 { color: #333; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Point Deformable Mamba 可视化结果</h1>
            <p>本页面展示了Point Deformable Mamba模型中不同扫描方法的可视化结果。</p>

            <h2>扫描方法说明</h2>
            <ul>
                <li><strong>希尔伯特曲线扫描 (Hilbert Curve)</strong>: 使用希尔伯特曲线对3D空间进行序列化</li>
                <li><strong>反希尔伯特扫描 (Hilbert-Trans)</strong>: 交换坐标轴后的希尔伯特曲线扫描</li>
                <li><strong>可变形扫描 (Deformable Scanning)</strong>: 基于点云特征自适应调整的扫描顺序</li>
            </ul>
    """

    for sample_idx in range(num_samples):
        html_content += f"""
            <div class="sample-section">
                <h2>样本 {sample_idx}</h2>
                <div class="visualization-grid">
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_original.html" target="_blank">原始点云</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_centers.html" target="_blank">分组中心点</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert.html" target="_blank">希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert_trans.html" target="_blank">反希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_deformable.html" target="_blank">可变形扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_comparison.html" target="_blank">对比可视化</a>
                    </div>
                </div>
            </div>
        """

    html_content += """
        </div>
    </body>
    </html>
    """

    with open(os.path.join(output_dir, "summary.html"), "w", encoding="utf-8") as f:
        f.write(html_content)

    try:
        # 可视化多个样本
        for sample_idx in range(args.num_samples):
            print(f"\n处理样本 {sample_idx + 1}/{args.num_samples}")

            # 获取可视化数据
            vis_data = visualizer.visualize_sample(sample_idx)
            if vis_data is None:
                continue

            # 创建原始点云可视化
            fig_original = visualizer.create_3d_visualization(
                vis_data['points'],
                title=f"Sample {sample_idx} - Original Point Cloud (Label: {vis_data['label']})"
            )
            fig_original.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_original.html"))

            # 创建中心点可视化
            fig_centers = visualizer.create_3d_visualization(
                vis_data['center_points'],
                title=f"Sample {sample_idx} - Group Centers",
                colors='red',
                size=6
            )
            fig_centers.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_centers.html"))

            # 创建希尔伯特扫描路径可视化
            fig_hilbert = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['hilbert_order'],
                title=f"Sample {sample_idx} - Hilbert Curve Scanning"
            )
            fig_hilbert.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert.html"))

            # 创建反希尔伯特扫描路径可视化
            fig_hilbert_trans = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['hilbert_trans_order'],
                title=f"Sample {sample_idx} - Hilbert-Trans Scanning"
            )
            fig_hilbert_trans.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert_trans.html"))

            # 创建可变形扫描路径可视化
            fig_deformable = visualizer.create_scanning_path_visualization(
                vis_data['center_points'],
                vis_data['deform_order'],
                title=f"Sample {sample_idx} - Deformable Scanning"
            )
            fig_deformable.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_deformable.html"))

            # 创建对比可视化
            fig_comparison = visualizer.create_comparison_visualization(
                vis_data['center_points'],
                vis_data['hilbert_order'],
                vis_data['hilbert_trans_order'],
                vis_data['deform_order']
            )
            fig_comparison.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_comparison.html"))

            print(f"样本 {sample_idx} 可视化完成，文件保存在 {args.output_dir}")

        # 创建总结HTML文件
        create_summary_html(args.output_dir, args.num_samples)

        print(f"\n所有可视化完成！")
        print(f"请打开 {args.output_dir}/summary.html 查看结果")

    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_summary_html(output_dir, num_samples):
    """创建总结HTML文件"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 可视化结果</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .sample-section { margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; }
            .visualization-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .viz-item { text-align: center; }
            .viz-item a { display: block; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 5px; }
            .viz-item a:hover { background: #e0e0e0; }
            h1, h2 { color: #333; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Point Deformable Mamba 可视化结果</h1>
            <p>本页面展示了Point Deformable Mamba模型中不同扫描方法的可视化结果。</p>

            <h2>扫描方法说明</h2>
            <ul>
                <li><strong>希尔伯特曲线扫描 (Hilbert Curve)</strong>: 使用希尔伯特曲线对3D空间进行序列化</li>
                <li><strong>反希尔伯特扫描 (Hilbert-Trans)</strong>: 交换坐标轴后的希尔伯特曲线扫描</li>
                <li><strong>可变形扫描 (Deformable Scanning)</strong>: 基于点云特征自适应调整的扫描顺序</li>
            </ul>
    """

    for sample_idx in range(num_samples):
        html_content += f"""
            <div class="sample-section">
                <h2>样本 {sample_idx}</h2>
                <div class="visualization-grid">
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_original.html" target="_blank">原始点云</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_centers.html" target="_blank">分组中心点</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert.html" target="_blank">希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert_trans.html" target="_blank">反希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_deformable.html" target="_blank">可变形扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_comparison.html" target="_blank">对比可视化</a>
                    </div>
                </div>
            </div>
        """

    html_content += """
        </div>
    </body>
    </html>
    """

    with open(os.path.join(output_dir, "summary.html"), "w", encoding="utf-8") as f:
        f.write(html_content)
