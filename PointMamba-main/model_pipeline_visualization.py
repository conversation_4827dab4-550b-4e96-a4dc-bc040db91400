#!/usr/bin/env python3
"""
Point Deformable Mamba 完整模型流程可视化
从ScanObjectNN数据集读取 -> FPS采样 -> 模型处理 -> 扫描可视化
"""

import os
import sys
import numpy as np
import torch
import h5py
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型相关模块
try:
    from models.point_deformable_mamba import PointDeformableMamba
    from models.point_mamba_scan import Group, Encoder, serialization_func
    from utils.misc import fps
    MODELS_AVAILABLE = True
    print("成功导入完整模型模块")
except ImportError as e:
    print(f"无法导入模型模块: {e}")
    print("将使用简化版本")
    MODELS_AVAILABLE = False

class ModelPipelineVisualizer:
    """完整模型流程可视化器"""
    
    def __init__(self, data_path="data/ScanObjectNN/main_split_nobg", device="cuda"):
        self.data_path = data_path
        self.device = device if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        
        # 设置模型配置
        self.setup_model_config()
        
    def setup_model_config(self):
        """设置模型配置"""
        class Config:
            def __init__(self):
                self.trans_dim = 192
                self.depth = 8
                self.cls_dim = 15
                self.num_group = 256
                self.group_size = 32
                self.encoder_dims = 192
                self.drop_path = 0.1
                self.use_cls_token = False
                self.max_head = False
                self.avg_head = True
        
        self.config = Config()
        
        # 如果模型可用，创建模型实例
        if MODELS_AVAILABLE:
            try:
                self.model = PointDeformableMamba(self.config)
                self.model.eval()
                if self.device == "cuda":
                    self.model = self.model.cuda()
                print("模型创建成功")
            except Exception as e:
                print(f"模型创建失败: {e}")
                self.model = None
        else:
            self.model = None
            
    def load_scanobjectnn_sample(self, sample_idx=0, subset="test"):
        """从ScanObjectNN数据集加载单个样本"""
        if subset == "test":
            h5_file = os.path.join(self.data_path, "test_objectdataset.h5")
        else:
            h5_file = os.path.join(self.data_path, "training_objectdataset.h5")
            
        if not os.path.exists(h5_file):
            print(f"数据文件不存在: {h5_file}")
            return None, None
            
        with h5py.File(h5_file, 'r') as f:
            points = np.array(f['data'][sample_idx]).astype(np.float32)
            label = np.array(f['label'][sample_idx]).astype(int)
            
        print(f"原始点云形状: {points.shape}, 标签: {label}")
        return points, label
        
    def apply_fps_sampling(self, points, npoints=1024):
        """应用FPS采样"""
        points_tensor = torch.from_numpy(points).float().unsqueeze(0)
        
        if self.device == "cuda":
            points_tensor = points_tensor.cuda()
            
        # 应用FPS采样
        try:
            if MODELS_AVAILABLE:
                sampled_points = fps(points_tensor, npoints)
                sampled_points = sampled_points.squeeze(0).cpu().numpy()
            else:
                # 简化版本：随机采样
                indices = np.random.choice(len(points), npoints, replace=False)
                sampled_points = points[indices]
                
            print(f"FPS采样后点云形状: {sampled_points.shape}")
            return sampled_points
            
        except Exception as e:
            print(f"FPS采样失败: {e}")
            # 回退到随机采样
            indices = np.random.choice(len(points), npoints, replace=False)
            return points[indices]
            
    def normalize_point_cloud(self, points):
        """标准化点云"""
        # 中心化
        centroid = np.mean(points, axis=0)
        points = points - centroid
        
        # 缩放到单位球
        max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
        if max_dist > 0:
            points = points / max_dist
            
        return points
        
    def process_through_model(self, points):
        """通过完整模型处理点云"""
        if not MODELS_AVAILABLE or self.model is None:
            print("模型不可用，使用简化处理")
            return self.simple_processing(points)
            
        try:
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            if self.device == "cuda":
                points_tensor = points_tensor.cuda()
                
            with torch.no_grad():
                # 通过模型的分组和编码步骤
                neighborhood, center = self.model.group_divider(points_tensor)
                group_features = self.model.encoder(neighborhood)
                pos_embed = self.model.pos_embed(center)
                
                # 应用可变形扫描
                deformed_features, deform_indices = self.model.deformable_scanning(center, group_features)
                
                # 标准希尔伯特扫描
                _, hilbert_order, _, hilbert_features, hilbert_pos = serialization_func(
                    center, group_features, pos_embed, 'hilbert'
                )
                
                # 反希尔伯特扫描
                _, hilbert_trans_order, _, hilbert_trans_features, hilbert_trans_pos = serialization_func(
                    center, group_features, pos_embed, 'hilbert-trans'
                )
                
                # 转换为numpy数组
                result = {
                    'center_points': center.squeeze(0).cpu().numpy(),
                    'group_features': group_features.squeeze(0).cpu().numpy(),
                    'hilbert_order': hilbert_order.squeeze(0).cpu().numpy(),
                    'hilbert_trans_order': hilbert_trans_order.squeeze(0).cpu().numpy(),
                    'deform_order': deform_indices.squeeze(0).cpu().numpy(),
                    'neighborhood': neighborhood.squeeze(0).cpu().numpy()
                }
                
                print(f"模型处理完成，生成 {len(result['center_points'])} 个分组中心点")
                return result
                
        except Exception as e:
            print(f"模型处理失败: {e}")
            return self.simple_processing(points)
            
    def simple_processing(self, points):
        """简化的处理流程"""
        # 简单分组：将点云分成若干组
        num_groups = min(256, len(points) // 4)
        group_size = len(points) // num_groups
        
        center_points = []
        for i in range(num_groups):
            start_idx = i * group_size
            end_idx = min((i + 1) * group_size, len(points))
            group_points = points[start_idx:end_idx]
            center = np.mean(group_points, axis=0)
            center_points.append(center)
            
        center_points = np.array(center_points)
        
        # 简单的扫描顺序
        def simple_hilbert_order(points, grid_size=0.02):
            scaled_coord = points / grid_size
            grid_coord = np.floor(scaled_coord).astype(np.int64)
            
            def z_order_key(coord):
                x, y, z = coord
                return (x & 0xFF) | ((y & 0xFF) << 8) | ((z & 0xFF) << 16)
            
            z_values = [z_order_key(coord) for coord in grid_coord]
            return np.argsort(z_values)
        
        hilbert_order = simple_hilbert_order(center_points)
        hilbert_trans_order = hilbert_order[::-1]
        deform_order = np.arange(len(center_points))
        np.random.shuffle(deform_order)
        
        result = {
            'center_points': center_points,
            'group_features': np.random.randn(len(center_points), 192),
            'hilbert_order': hilbert_order,
            'hilbert_trans_order': hilbert_trans_order,
            'deform_order': deform_order,
            'neighborhood': points[:len(center_points) * 4].reshape(len(center_points), 4, 3)
        }
        
        print(f"简化处理完成，生成 {len(center_points)} 个分组中心点")
        return result

    def create_pipeline_visualization(self, original_points, fps_points, model_result, 
                                    title_prefix="", save_path=None):
        """创建完整流程的可视化"""
        fig = make_subplots(
            rows=2, cols=3,
            specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}, {'type': 'scatter3d'}],
                   [{'type': 'scatter3d'}, {'type': 'scatter3d'}, {'type': 'scatter3d'}]],
            subplot_titles=(
                f'{title_prefix}Original Point Cloud', 
                f'{title_prefix}After FPS Sampling',
                f'{title_prefix}Group Centers',
                f'{title_prefix}Hilbert Scanning', 
                f'{title_prefix}Hilbert-Trans Scanning',
                f'{title_prefix}Deformable Scanning'
            ),
            vertical_spacing=0.1,
            horizontal_spacing=0.05
        )
        
        # 1. 原始点云
        fig.add_trace(go.Scatter3d(
            x=original_points[:500, 0], y=original_points[:500, 1], z=original_points[:500, 2],
            mode='markers',
            marker=dict(size=2, color='blue', opacity=0.6),
            name='Original'
        ), row=1, col=1)
        
        # 2. FPS采样后的点云
        fig.add_trace(go.Scatter3d(
            x=fps_points[:, 0], y=fps_points[:, 1], z=fps_points[:, 2],
            mode='markers',
            marker=dict(size=3, color='green', opacity=0.8),
            name='FPS Sampled'
        ), row=1, col=2)
        
        # 3. 分组中心点
        center_points = model_result['center_points']
        fig.add_trace(go.Scatter3d(
            x=center_points[:, 0], y=center_points[:, 1], z=center_points[:, 2],
            mode='markers',
            marker=dict(size=6, color='red', opacity=0.9),
            name='Group Centers'
        ), row=1, col=3)
        
        # 4-6. 不同扫描方法
        max_display = min(100, len(center_points))
        
        # 希尔伯特扫描
        hilbert_points = center_points[model_result['hilbert_order'][:max_display]]
        colors = np.arange(max_display)
        fig.add_trace(go.Scatter3d(
            x=hilbert_points[:, 0], y=hilbert_points[:, 1], z=hilbert_points[:, 2],
            mode='lines+markers',
            line=dict(color='red', width=3),
            marker=dict(size=4, color=colors, colorscale='Viridis'),
            name='Hilbert'
        ), row=2, col=1)
        
        # 反希尔伯特扫描
        hilbert_trans_points = center_points[model_result['hilbert_trans_order'][:max_display]]
        fig.add_trace(go.Scatter3d(
            x=hilbert_trans_points[:, 0], y=hilbert_trans_points[:, 1], z=hilbert_trans_points[:, 2],
            mode='lines+markers',
            line=dict(color='green', width=3),
            marker=dict(size=4, color=colors, colorscale='Plasma'),
            name='Hilbert-Trans'
        ), row=2, col=2)
        
        # 可变形扫描
        deform_points = center_points[model_result['deform_order'][:max_display]]
        fig.add_trace(go.Scatter3d(
            x=deform_points[:, 0], y=deform_points[:, 1], z=deform_points[:, 2],
            mode='lines+markers',
            line=dict(color='purple', width=3),
            marker=dict(size=4, color=colors, colorscale='Cividis'),
            name='Deformable'
        ), row=2, col=3)
        
        fig.update_layout(
            title=f"Point Deformable Mamba Complete Pipeline Visualization",
            height=800,
            width=1400,
            showlegend=False
        )
        
        if save_path:
            fig.write_html(save_path)
            print(f"完整流程可视化已保存: {save_path}")
        
        return fig

    def create_pipeline_image(self, original_points, fps_points, model_result,
                            title_prefix="", save_path=None):
        """创建完整流程的静态图片"""
        fig = plt.figure(figsize=(20, 12))

        # 1. 原始点云
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')
        ax1.scatter(original_points[:500, 0], original_points[:500, 1], original_points[:500, 2],
                   c='blue', s=8, alpha=0.6)
        ax1.set_title(f'{title_prefix}Original Point Cloud\n({len(original_points)} points)',
                     fontsize=12, fontweight='bold')
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        ax1.set_zlabel('Z')

        # 2. FPS采样后的点云
        ax2 = fig.add_subplot(2, 3, 2, projection='3d')
        ax2.scatter(fps_points[:, 0], fps_points[:, 1], fps_points[:, 2],
                   c='green', s=15, alpha=0.8)
        ax2.set_title(f'{title_prefix}After FPS Sampling\n({len(fps_points)} points)',
                     fontsize=12, fontweight='bold')
        ax2.set_xlabel('X')
        ax2.set_ylabel('Y')
        ax2.set_zlabel('Z')

        # 3. 分组中心点
        ax3 = fig.add_subplot(2, 3, 3, projection='3d')
        center_points = model_result['center_points']
        ax3.scatter(center_points[:, 0], center_points[:, 1], center_points[:, 2],
                   c='red', s=25, alpha=0.9)
        ax3.set_title(f'{title_prefix}Group Centers\n({len(center_points)} groups)',
                     fontsize=12, fontweight='bold')
        ax3.set_xlabel('X')
        ax3.set_ylabel('Y')
        ax3.set_zlabel('Z')

        # 4-6. 不同扫描方法
        max_display = min(80, len(center_points))
        colors = np.arange(max_display)

        # 希尔伯特扫描
        ax4 = fig.add_subplot(2, 3, 4, projection='3d')
        hilbert_points = center_points[model_result['hilbert_order'][:max_display]]
        scatter4 = ax4.scatter(hilbert_points[:, 0], hilbert_points[:, 1], hilbert_points[:, 2],
                              c=colors, s=30, cmap='viridis', alpha=0.9)
        ax4.plot(hilbert_points[:, 0], hilbert_points[:, 1], hilbert_points[:, 2],
                'r-', linewidth=2, alpha=0.7)
        ax4.set_title(f'{title_prefix}Hilbert Scanning', fontsize=12, fontweight='bold')
        ax4.set_xlabel('X')
        ax4.set_ylabel('Y')
        ax4.set_zlabel('Z')

        # 反希尔伯特扫描
        ax5 = fig.add_subplot(2, 3, 5, projection='3d')
        hilbert_trans_points = center_points[model_result['hilbert_trans_order'][:max_display]]
        scatter5 = ax5.scatter(hilbert_trans_points[:, 0], hilbert_trans_points[:, 1], hilbert_trans_points[:, 2],
                              c=colors, s=30, cmap='plasma', alpha=0.9)
        ax5.plot(hilbert_trans_points[:, 0], hilbert_trans_points[:, 1], hilbert_trans_points[:, 2],
                'g-', linewidth=2, alpha=0.7)
        ax5.set_title(f'{title_prefix}Hilbert-Trans Scanning', fontsize=12, fontweight='bold')
        ax5.set_xlabel('X')
        ax5.set_ylabel('Y')
        ax5.set_zlabel('Z')

        # 可变形扫描
        ax6 = fig.add_subplot(2, 3, 6, projection='3d')
        deform_points = center_points[model_result['deform_order'][:max_display]]
        scatter6 = ax6.scatter(deform_points[:, 0], deform_points[:, 1], deform_points[:, 2],
                              c=colors, s=30, cmap='cividis', alpha=0.9)
        ax6.plot(deform_points[:, 0], deform_points[:, 1], deform_points[:, 2],
                'purple', linewidth=2, alpha=0.7)
        ax6.set_title(f'{title_prefix}Deformable Scanning', fontsize=12, fontweight='bold')
        ax6.set_xlabel('X')
        ax6.set_ylabel('Y')
        ax6.set_zlabel('Z')

        # 设置所有子图的视角
        for ax in [ax1, ax2, ax3, ax4, ax5, ax6]:
            ax.view_init(elev=20, azim=45)

        # 添加总标题
        fig.suptitle('Point Deformable Mamba Complete Pipeline', fontsize=16, fontweight='bold')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"完整流程图片已保存: {save_path}")

        plt.close()
        return fig

def main():
    parser = argparse.ArgumentParser(description='Point Deformable Mamba 完整模型流程可视化')
    parser.add_argument('--data_path', type=str, default='data/ScanObjectNN/main_split_nobg',
                        help='ScanObjectNN数据集路径')
    parser.add_argument('--sample_idx', type=int, default=0,
                        help='样本索引')
    parser.add_argument('--npoints', type=int, default=1024,
                        help='FPS采样点数')
    parser.add_argument('--output_dir', type=str, default='pipeline_output',
                        help='输出目录')
    parser.add_argument('--device', type=str, default='cuda',
                        help='计算设备')

    args = parser.parse_args()

    print("Point Deformable Mamba 完整模型流程可视化")
    print("=" * 60)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建可视化器
    visualizer = ModelPipelineVisualizer(data_path=args.data_path, device=args.device)

    try:
        # 1. 加载原始数据
        print(f"\n步骤1: 从ScanObjectNN加载样本 {args.sample_idx}")
        original_points, label = visualizer.load_scanobjectnn_sample(args.sample_idx)
        if original_points is None:
            return

        # 2. 标准化
        print("步骤2: 标准化点云")
        original_points = visualizer.normalize_point_cloud(original_points)

        # 3. FPS采样
        print(f"步骤3: 应用FPS采样到 {args.npoints} 个点")
        fps_points = visualizer.apply_fps_sampling(original_points, args.npoints)

        # 4. 通过模型处理
        print("步骤4: 通过PointDeformableMamba模型处理")
        model_result = visualizer.process_through_model(fps_points)

        # 5. 生成可视化
        print("步骤5: 生成可视化结果")

        # Web可视化
        fig_web = visualizer.create_pipeline_visualization(
            original_points, fps_points, model_result,
            title_prefix=f"Sample {args.sample_idx} (Label: {label}) - ",
            save_path=os.path.join(args.output_dir, f"pipeline_sample_{args.sample_idx}.html")
        )

        # 图片可视化
        fig_img = visualizer.create_pipeline_image(
            original_points, fps_points, model_result,
            title_prefix=f"Sample {args.sample_idx} (Label: {label}) - ",
            save_path=os.path.join(args.output_dir, f"pipeline_sample_{args.sample_idx}.png")
        )

        # 创建详细信息文件
        create_pipeline_info(args.output_dir, args.sample_idx, label,
                           len(original_points), len(fps_points),
                           len(model_result['center_points']))

        print(f"\n✅ 完整流程可视化完成！")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"🌐 Web可视化: pipeline_sample_{args.sample_idx}.html")
        print(f"🖼️ 图片: pipeline_sample_{args.sample_idx}.png")
        print(f"📄 详细信息: pipeline_info.md")

    except Exception as e:
        print(f"❌ 可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_pipeline_info(output_dir, sample_idx, label, original_count, fps_count, group_count):
    """创建流程信息文件"""
    info_content = f"""# Point Deformable Mamba 模型流程信息

## 样本信息
- **样本索引**: {sample_idx}
- **样本标签**: {label}
- **数据集**: ScanObjectNN

## 处理流程

### 1. 原始点云
- **点数**: {original_count}
- **来源**: ScanObjectNN数据集
- **预处理**: 中心化和归一化

### 2. FPS采样
- **采样后点数**: {fps_count}
- **采样方法**: Farthest Point Sampling (FPS)
- **目的**: 保持点云几何特征的同时减少计算量

### 3. 模型处理
- **分组数量**: {group_count}
- **分组方法**: PointDeformableMamba的Group模块
- **特征编码**: Encoder模块提取局部特征

### 4. 扫描方法
- **希尔伯特扫描**: 使用3D希尔伯特曲线序列化分组中心点
- **反希尔伯特扫描**: 交换坐标轴后的希尔伯特扫描
- **可变形扫描**: 基于点云特征的自适应扫描顺序

## 可视化说明

### Web可视化 (HTML)
- 交互式3D可视化
- 支持旋转、缩放、重置
- 清晰显示每个处理步骤

### 静态图片 (PNG)
- 高分辨率300 DPI
- 六个子图展示完整流程
- 适合学术论文和报告

## 技术细节

### 模型配置
- **Transformer维度**: 192
- **深度**: 8
- **分组大小**: 32
- **编码器维度**: 192

### 扫描特点
- **颜色编码**: 扫描顺序用颜色渐变表示
- **路径连线**: 红色线条显示扫描路径
- **空间局部性**: 希尔伯特曲线保持空间邻近关系

---
生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    with open(os.path.join(output_dir, "pipeline_info.md"), "w", encoding="utf-8") as f:
        f.write(info_content)

if __name__ == "__main__":
    main()
