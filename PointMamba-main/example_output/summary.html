
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 可视化结果</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .sample-section { margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
            .visualization-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .viz-item { text-align: center; }
            .viz-item a { display: block; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); text-decoration: none; color: white; border-radius: 8px; transition: transform 0.2s; }
            .viz-item a:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
            h1, h2 { color: #333; }
            .description { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 Point Deformable Mamba 可视化结果</h1>
            <div class="description">
                <p>本页面展示了Point Deformable Mamba模型中不同扫描方法的可视化结果。通过对比不同的扫描策略，可以更好地理解模型如何处理3D点云数据。</p>
            </div>

            <h2>📋 扫描方法说明</h2>
            <ul>
                <li><strong>希尔伯特曲线扫描 (Hilbert Curve)</strong>: 使用希尔伯特曲线对3D空间进行序列化，保持空间局部性</li>
                <li><strong>反希尔伯特扫描 (Hilbert-Trans)</strong>: 交换坐标轴后的希尔伯特曲线扫描，提供不同的空间遍历顺序</li>
                <li><strong>可变形扫描 (Deformable Scanning)</strong>: 基于点云特征自适应调整的扫描顺序，能够根据数据特点动态优化</li>
            </ul>
    
            <div class="sample-section">
                <h2>📊 样本 0</h2>
                <div class="visualization-grid">
                    <div class="viz-item">
                        <a href="sample_0_original.html" target="_blank">🔵 原始点云</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_0_hilbert.html" target="_blank">🔴 希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_0_hilbert_trans.html" target="_blank">🟢 反希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_0_deformable.html" target="_blank">🟣 可变形扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_0_comparison.html" target="_blank">📈 对比可视化</a>
                    </div>
                </div>
            </div>
        
            <div class="description">
                <h3>💡 使用说明</h3>
                <p>点击上方的链接打开对应的3D可视化页面。在可视化页面中，您可以：</p>
                <ul>
                    <li>🖱️ 拖拽旋转视角</li>
                    <li>🔍 滚轮缩放</li>
                    <li>📱 双击重置视角</li>
                    <li>🎨 观察不同扫描路径的颜色编码</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    