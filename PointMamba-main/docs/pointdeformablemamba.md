# PointDeformableMamba: Deformable Scanning and Enhanced <PERSON>mba for Point Cloud Processing

## Introduction

PointDeformableMamba enhances the PointMamba architecture by incorporating deformable scanning and multi-path fusion techniques inspired by DefMamba. This allows the model to dynamically adjust scanning paths based on point cloud content, prioritizing important regions and leading to more effective feature learning.

## Key Components

### 1. Deformable Scanning (DS)

The Deformable Scanning module enables content-adaptive serialization of point cloud data by generating two types of offsets:

#### Point Offset Network
- **Input**: Group features of shape (B, G, C)
- **Structure**:
  - Multiple stages of 1D convolutions with residual connections
  - Channel Attention mechanism for global context integration
  - Output: Spatial offsets (B, G, 3) and index offsets (B, G, 1)

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  Input (B,G,C)│     │ 1D Conv + GELU│     │Channel        │     │Output Spatial │
│  Features     │────►│ + LayerNorm   │────►│Attention      │────►│& Index Offsets│
└───────────────┘     └───────────────┘     └───────────────┘     └───────────────┘
```

#### Point Spatial Deformation
- Generates deformed points: p_deformed = p_original + spatial_offsets
- Uses <PERSON>NN to find nearest neighbors of original points to deformed points
- Performs feature aggregation with distance-based weights
- Applies feature transformation with residual connection

```
Original Points & Features       Deformed Points              Aggregated Features 
      ●─────┐                        ◆                          ●───────────
      │     │                       ╱ ╲                         │         │
      ●     │                      ╱   ╲                        ●         │
      │     │      Offset         ╱     ╲     KNN &            │         │
      ●     │ ───────────────►   ◆       ◆  Aggregation       ●          │
      │     │                     ╲     ╱  ───────────►        │          │
      ●     │                      ╲   ╱                       ●          │
      │     │                       ╲ ╱                        │          │
      ●─────┘                        ◆                         ●───────────
```

#### Index Deformation & Reordering
- Generates reference indices normalized to [-1, 1]
- Applies index offsets: deformed_indices = reference_indices + index_offsets
- Uses sorting to determine new scanning order
- Reorders features according to this new order

```
  Reference Indices        Index Offsets         Deformed Indices         Sorted Order
┌─────────────────┐     ┌─────────────┐       ┌─────────────────┐     ┌─────────────┐
│  0  1  2  3  4  │     │ +.1 -.2 +.4 │       │ 0.1 0.8 2.4 2.6 │     │  0  1  3  4 │
│ -.5 -.25 0 +.25 │  +  │ -.3 +.6 -.1 │  =    │ -.8 +.35 -.1 +.4│  →  │  2  0  1  3 │
└─────────────────┘     └─────────────┘       └─────────────────┘     └─────────────┘
```

### 2. Deformable Mamba Block

The Deformable Mamba Block enhances the standard Mamba block with multi-path processing:

- **Three Parallel Branches**:
  - Forward Mamba: Processes features in standard forward order
  - Backward Mamba: Processes features in reversed order
  - Deformable Mamba: Processes features reordered by deformable scanning

- **Feature Fusion**:
  - Concatenates outputs from all three branches
  - Processes through an MLP for dimensionality reduction and feature integration
  - Applies residual connection with drop path regularization

```
                 ┌───────────────────────┐
                 │   Forward Mamba       │
┌─────────┐      └───────────┬───────────┘      ┌─────────────┐      ┌─────────┐
│         │                   │                  │             │      │         │
│ Input   │───────►    ┌──────┴───────────┐     │Feature      │      │ Output  │
│ Features│───────────►│   Backward Mamba │────►│Fusion       │─────►│ Features│
│         │      │     └──────┬───────────┘     │(Concat+MLP) │      │         │
└─────────┘      │            │                  │             │      └─────────┘
                 │     ┌──────┴───────────┐     └─────────────┘
                 │     │ Deformable Mamba │            ▲
                 └────►└──────────────────┘            │
                                                        │
                                                  Residual Connection
```

## Architecture

The overall PointDeformableMamba architecture follows these steps:

1. **Point Grouping**: Uses FPS and KNN to sample and group points (unchanged from PointMamba)
2. **Feature Encoding**: Applies PointNet-like encoder to extract group features
3. **Dual Serialization**:
   - Standard serialization using Hilbert curve (for stability)
   - Deformable serialization using the DS module (for adaptivity)
4. **Feature Processing**:
   - Processes through multiple DeformableMambaBlocks
   - Each block integrates standard and deformable features
5. **Classification Head**:
   - Aggregates features using optional CLS token, average pooling, or max pooling
   - Processes through MLP for final classification

## Advantages over PointMamba

1. **Content-Adaptive Processing**: Unlike PointMamba's fixed Hilbert curve scanning, PointDeformableMamba learns to scan points based on their semantic importance.

2. **Multi-Path Integration**: Fuses information from multiple scanning paths (forward, backward, deformable), capturing multi-scale patterns in point clouds.

3. **Enhanced Local-Global Interactions**: Spatial deformation allows each point to aggregate features from semantically meaningful regions, not just geometrically close ones.

4. **Robustness to Point Density Variations**: Adaptive scanning helps the model focus on informative regions regardless of point sampling density.

## Implementation Details

- **Channel Attention**: Helps reduce channel redundancy and integrate global context
- **Feature Transformation**: Applies transformation to aggregated features with residual connection
- **Gradient Handling**: Deformable scanning introduces gradient discontinuities; this is managed through appropriate operation ordering
- **Drop Path**: Applied for regularization and to prevent over-reliance on specific branches

## Training

The model can be trained using:

```bash
python tools/train_deformable_mamba.py --config cfgs/scanobjectnn_models/pointdeformablemamba.yaml --gpu 0
```

## Testing

Evaluation can be performed using:

```bash
python tools/train_deformable_mamba.py --config cfgs/scanobjectnn_models/pointdeformablemamba.yaml --gpu 0 --test --checkpoint_path /path/to/checkpoint.pth
```

## References

1. PointMamba: Point Cloud Learning via Local State Space Model (https://arxiv.org/abs/2403.00422)
2. DefMamba: Improving Mamba with Deformable Scanning (https://arxiv.org/abs/2403.10528) 