
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 批量流程可视化结果</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                margin: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container { 
                max-width: 1400px; 
                margin: 0 auto; 
                background-color: white; 
                padding: 30px; 
                border-radius: 15px; 
                box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
            }
            .sample-grid { 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); 
                gap: 30px; 
                margin-bottom: 40px;
            }
            .sample-card { 
                border: 2px solid #e0e0e0; 
                padding: 25px; 
                border-radius: 12px; 
                background: linear-gradient(145deg, #f8f9fa, #e9ecef);
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .sample-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }
            .sample-title {
                font-size: 20px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
                padding: 10px;
                background: white;
                border-radius: 8px;
            }
            .file-links { 
                display: flex; 
                flex-direction: column; 
                gap: 12px; 
            }
            .file-link { 
                display: block; 
                padding: 12px 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                text-decoration: none; 
                color: white; 
                border-radius: 8px; 
                text-align: center;
                font-weight: 500;
                transition: all 0.3s ease;
            }
            .file-link:hover { 
                transform: scale(1.05);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            }
            .stats-section {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 30px;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            .stat-item {
                text-align: center;
                padding: 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #667eea;
            }
            .stat-label {
                color: #666;
                margin-top: 5px;
            }
            .pipeline-info {
                background: #e3f2fd;
                padding: 20px;
                border-radius: 10px;
                border-left: 5px solid #2196f3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔬 Point Deformable Mamba 批量流程可视化</h1>
                <p>完整的模型处理流程：ScanObjectNN → FPS采样 → 模型处理 → 扫描可视化</p>
            </div>
            
            <div class="stats-section">
                <h2>📊 处理统计</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">处理样本数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2048</div>
                        <div class="stat-label">原始点数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1024</div>
                        <div class="stat-label">FPS采样点数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">256</div>
                        <div class="stat-label">分组中心点数</div>
                    </div>
                </div>
            </div>
            
            <div class="pipeline-info">
                <h3>🔄 处理流程说明</h3>
                <ol>
                    <li><strong>数据加载</strong>: 从ScanObjectNN数据集加载原始点云（2048个点）</li>
                    <li><strong>预处理</strong>: 中心化和归一化处理</li>
                    <li><strong>FPS采样</strong>: 使用最远点采样减少到1024个点</li>
                    <li><strong>模型处理</strong>: 通过PointDeformableMamba模型进行分组和特征提取</li>
                    <li><strong>扫描可视化</strong>: 展示希尔伯特、反希尔伯特和可变形扫描方法</li>
                </ol>
            </div>
            
            <h2>📁 样本可视化结果</h2>
            <div class="sample-grid">
    
                <div class="sample-card">
                    <div class="sample-title">样本 0 (标签: 0)</div>
                    <div class="file-links">
                        <a href="pipeline_sample_0.html" target="_blank" class="file-link">
                            🌐 交互式Web可视化
                        </a>
                        <a href="pipeline_sample_0.png" target="_blank" class="file-link">
                            🖼️ 高分辨率图片
                        </a>
                    </div>
                </div>
        
                <div class="sample-card">
                    <div class="sample-title">样本 1 (标签: 0)</div>
                    <div class="file-links">
                        <a href="pipeline_sample_1.html" target="_blank" class="file-link">
                            🌐 交互式Web可视化
                        </a>
                        <a href="pipeline_sample_1.png" target="_blank" class="file-link">
                            🖼️ 高分辨率图片
                        </a>
                    </div>
                </div>
        
                <div class="sample-card">
                    <div class="sample-title">样本 2 (标签: 0)</div>
                    <div class="file-links">
                        <a href="pipeline_sample_2.html" target="_blank" class="file-link">
                            🌐 交互式Web可视化
                        </a>
                        <a href="pipeline_sample_2.png" target="_blank" class="file-link">
                            🖼️ 高分辨率图片
                        </a>
                    </div>
                </div>
        
            </div>
            
            <div class="pipeline-info">
                <h3>💡 使用说明</h3>
                <ul>
                    <li><strong>Web可视化</strong>: 点击"交互式Web可视化"链接，可以旋转、缩放观察3D点云</li>
                    <li><strong>图片查看</strong>: 点击"高分辨率图片"链接查看静态图片，适合保存和分享</li>
                    <li><strong>流程对比</strong>: 每个可视化都包含6个步骤的完整展示</li>
                    <li><strong>扫描分析</strong>: 观察不同扫描方法对同一点云的处理差异</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Point Deformable Mamba 可视化工具</p>
            </div>
        </div>
    </body>
    </html>
    