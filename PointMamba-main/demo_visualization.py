#!/usr/bin/env python3
"""
Point Deformable Mamba 演示可视化脚本
展示DS扫描、希尔伯特曲线和反希尔伯特曲线对点云的扫描过程
"""

import os
import sys
import numpy as np
import torch
import h5py
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 尝试导入模型相关模块
try:
    from models.point_mamba_scan import Group, Encoder, serialization_func
    from models.point_deformable_mamba import DeformableScanning
    MODELS_AVAILABLE = True
    print("成功导入模型模块")
except ImportError as e:
    print(f"无法导入模型模块: {e}")
    print("将使用简化的演示功能")
    MODELS_AVAILABLE = False

class PointCloudVisualizer:
    """点云可视化器"""
    
    def __init__(self, data_path="data/ScanObjectNN/main_split_nobg"):
        self.data_path = data_path
        
    def load_scanobjectnn_data(self, subset="test", num_samples=3):
        """从ScanObjectNN数据集加载点云数据"""
        if subset == "test":
            h5_file = os.path.join(self.data_path, "test_objectdataset.h5")
        else:
            h5_file = os.path.join(self.data_path, "training_objectdataset.h5")
            
        if not os.path.exists(h5_file):
            print(f"数据文件不存在: {h5_file}")
            return None, None
            
        with h5py.File(h5_file, 'r') as f:
            points = np.array(f['data'][:num_samples]).astype(np.float32)
            labels = np.array(f['label'][:num_samples]).astype(int)
            
        print(f"成功加载 {len(points)} 个点云样本，每个样本有 {points.shape[1]} 个点")
        return points, labels
        
    def normalize_point_cloud(self, points):
        """标准化点云"""
        centroid = np.mean(points, axis=0)
        points = points - centroid
        max_dist = np.max(np.sqrt(np.sum(points ** 2, axis=1)))
        points = points / max_dist
        return points
        
    def simple_hilbert_order(self, points, grid_size=0.02):
        """简化的希尔伯特排序"""
        scaled_coord = points / grid_size
        grid_coord = np.floor(scaled_coord).astype(np.int64)
        
        def z_order_key(coord):
            x, y, z = coord
            return (x & 0xFF) | ((y & 0xFF) << 8) | ((z & 0xFF) << 16)
        
        z_values = [z_order_key(coord) for coord in grid_coord]
        return np.argsort(z_values)
    
    def apply_real_hilbert_scanning(self, points):
        """应用真实的希尔伯特曲线扫描（如果模型可用）"""
        if not MODELS_AVAILABLE:
            return self.simple_hilbert_order(points), self.simple_hilbert_order(points)[::-1]
            
        try:
            # 使用真实的模型进行扫描
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            
            # 简化：直接使用点作为中心点
            center_points = points_tensor
            features = torch.randn(1, points.shape[0], 192)
            pos_embed = torch.randn(1, points.shape[0], 192)
            
            # 希尔伯特扫描
            _, hilbert_order, _, _, _ = serialization_func(
                center_points, features, pos_embed, 'hilbert'
            )
            
            # 反希尔伯特扫描
            _, hilbert_trans_order, _, _, _ = serialization_func(
                center_points, features, pos_embed, 'hilbert-trans'
            )
            
            return hilbert_order.squeeze(0).numpy(), hilbert_trans_order.squeeze(0).numpy()
            
        except Exception as e:
            print(f"使用真实模型时出错: {e}")
            return self.simple_hilbert_order(points), self.simple_hilbert_order(points)[::-1]
    
    def apply_deformable_scanning(self, points):
        """应用可变形扫描"""
        if not MODELS_AVAILABLE:
            # 简化版本：随机打乱顺序作为"可变形"扫描
            indices = np.arange(len(points))
            np.random.shuffle(indices)
            return indices
            
        try:
            points_tensor = torch.from_numpy(points).float().unsqueeze(0)
            features = torch.randn(1, points.shape[0], 192)
            
            deformable_scanner = DeformableScanning(feature_dim=192, hidden_dim=96, k=4)
            
            with torch.no_grad():
                _, deform_indices = deformable_scanner(points_tensor, features)
                
            return deform_indices.squeeze(0).numpy()
            
        except Exception as e:
            print(f"使用可变形扫描时出错: {e}")
            indices = np.arange(len(points))
            np.random.shuffle(indices)
            return indices

    def create_3d_visualization(self, points, title="Point Cloud", colors=None, size=3):
        """创建3D可视化"""
        fig = go.Figure()
        
        if colors is None:
            colors = 'blue'
            
        fig.add_trace(go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1], 
            z=points[:, 2],
            mode='markers',
            marker=dict(
                size=size,
                color=colors,
                colorscale='Viridis' if isinstance(colors, (list, np.ndarray)) else None,
                showscale=True if isinstance(colors, (list, np.ndarray)) else False
            ),
            name=title
        ))
        
        fig.update_layout(
            title=title,
            scene=dict(
                xaxis_title='X',
                yaxis_title='Y',
                zaxis_title='Z',
                aspectmode='cube'
            ),
            width=800,
            height=600
        )
        
        return fig

    def create_scanning_path_visualization(self, points, scan_order, title="Scanning Path"):
        """创建扫描路径可视化"""
        fig = go.Figure()
        
        # 添加点云
        fig.add_trace(go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1],
            z=points[:, 2],
            mode='markers',
            marker=dict(
                size=4,
                color='lightblue',
                opacity=0.6
            ),
            name='Points'
        ))
        
        # 添加扫描路径（限制显示点数以提高性能）
        max_points = min(200, len(scan_order))
        ordered_points = points[scan_order[:max_points]]
        
        fig.add_trace(go.Scatter3d(
            x=ordered_points[:, 0],
            y=ordered_points[:, 1],
            z=ordered_points[:, 2],
            mode='lines+markers',
            line=dict(
                color='red',
                width=3
            ),
            marker=dict(
                size=6,
                color=np.arange(max_points),
                colorscale='Plasma',
                showscale=True,
                colorbar=dict(title="Scan Order")
            ),
            name='Scan Path'
        ))
        
        fig.update_layout(
            title=title,
            scene=dict(
                xaxis_title='X',
                yaxis_title='Y',
                zaxis_title='Z',
                aspectmode='cube'
            ),
            width=900,
            height=700
        )
        
        return fig
        
    def create_comparison_visualization(self, points, hilbert_order, hilbert_trans_order, deform_order):
        """创建不同扫描方法的对比可视化"""
        fig = make_subplots(
            rows=2, cols=2,
            specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}],
                   [{'type': 'scatter3d'}, {'type': 'scatter3d'}]],
            subplot_titles=('Original Point Cloud', 'Hilbert Curve Scanning', 
                          'Hilbert-Trans Scanning', 'Deformable Scanning'),
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )
        
        # 限制显示的点数
        max_display = min(100, len(points))
        
        # 原始点云
        fig.add_trace(go.Scatter3d(
            x=points[:max_display, 0], y=points[:max_display, 1], z=points[:max_display, 2],
            mode='markers',
            marker=dict(size=4, color='blue'),
            name='Original'
        ), row=1, col=1)
        
        # 希尔伯特扫描
        hilbert_points = points[hilbert_order[:max_display]]
        fig.add_trace(go.Scatter3d(
            x=hilbert_points[:, 0], y=hilbert_points[:, 1], z=hilbert_points[:, 2],
            mode='lines+markers',
            line=dict(color='red', width=2),
            marker=dict(size=3, color=np.arange(max_display), colorscale='Viridis'),
            name='Hilbert'
        ), row=1, col=2)
        
        # 反希尔伯特扫描
        hilbert_trans_points = points[hilbert_trans_order[:max_display]]
        fig.add_trace(go.Scatter3d(
            x=hilbert_trans_points[:, 0], y=hilbert_trans_points[:, 1], z=hilbert_trans_points[:, 2],
            mode='lines+markers',
            line=dict(color='green', width=2),
            marker=dict(size=3, color=np.arange(max_display), colorscale='Plasma'),
            name='Hilbert-Trans'
        ), row=2, col=1)
        
        # 可变形扫描
        deform_points = points[deform_order[:max_display]]
        fig.add_trace(go.Scatter3d(
            x=deform_points[:, 0], y=deform_points[:, 1], z=deform_points[:, 2],
            mode='lines+markers',
            line=dict(color='purple', width=2),
            marker=dict(size=3, color=np.arange(max_display), colorscale='Cividis'),
            name='Deformable'
        ), row=2, col=2)
        
        fig.update_layout(
            title="Point Cloud Scanning Methods Comparison",
            height=800,
            width=1200,
            showlegend=False
        )
        
        return fig

def main():
    parser = argparse.ArgumentParser(description='Point Deformable Mamba 演示可视化')
    parser.add_argument('--data_path', type=str, default='data/ScanObjectNN/main_split_nobg',
                        help='ScanObjectNN数据集路径')
    parser.add_argument('--num_samples', type=int, default=2,
                        help='要可视化的样本数量')
    parser.add_argument('--output_dir', type=str, default='demo_visualization_output',
                        help='输出目录')

    args = parser.parse_args()

    print("Point Deformable Mamba 演示可视化")
    print("=" * 50)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建可视化器
    visualizer = PointCloudVisualizer(data_path=args.data_path)

    try:
        # 加载数据
        points_data, labels = visualizer.load_scanobjectnn_data(num_samples=args.num_samples)
        if points_data is None:
            return

        # 处理每个样本
        for sample_idx in range(args.num_samples):
            print(f"\n处理样本 {sample_idx + 1}/{args.num_samples}")

            points = points_data[sample_idx]
            label = labels[sample_idx]

            # 标准化点云
            points = visualizer.normalize_point_cloud(points)

            # 为了演示效果，只使用前300个点
            points = points[:300]

            print(f"样本 {sample_idx}, 标签: {label}, 使用 {len(points)} 个点")

            # 应用不同的扫描方法
            hilbert_order, hilbert_trans_order = visualizer.apply_real_hilbert_scanning(points)
            deform_order = visualizer.apply_deformable_scanning(points)

            # 创建可视化
            # 1. 原始点云
            fig_original = visualizer.create_3d_visualization(
                points,
                title=f"Sample {sample_idx} - Original Point Cloud (Label: {label})"
            )
            fig_original.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_original.html"))

            # 2. 希尔伯特扫描
            fig_hilbert = visualizer.create_scanning_path_visualization(
                points,
                hilbert_order,
                title=f"Sample {sample_idx} - Hilbert Curve Scanning"
            )
            fig_hilbert.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert.html"))

            # 3. 反希尔伯特扫描
            fig_hilbert_trans = visualizer.create_scanning_path_visualization(
                points,
                hilbert_trans_order,
                title=f"Sample {sample_idx} - Hilbert-Trans Scanning"
            )
            fig_hilbert_trans.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_hilbert_trans.html"))

            # 4. 可变形扫描
            fig_deformable = visualizer.create_scanning_path_visualization(
                points,
                deform_order,
                title=f"Sample {sample_idx} - Deformable Scanning"
            )
            fig_deformable.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_deformable.html"))

            # 5. 对比可视化
            fig_comparison = visualizer.create_comparison_visualization(
                points,
                hilbert_order,
                hilbert_trans_order,
                deform_order
            )
            fig_comparison.write_html(os.path.join(args.output_dir, f"sample_{sample_idx}_comparison.html"))

            print(f"样本 {sample_idx} 可视化完成")

        # 创建总结HTML
        create_summary_html(args.output_dir, args.num_samples)

        print(f"\n所有可视化完成！")
        print(f"请打开 {args.output_dir}/summary.html 查看结果")

    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_summary_html(output_dir, num_samples):
    """创建总结HTML文件"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Point Deformable Mamba 可视化结果</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
            .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .sample-section {{ margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }}
            .visualization-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
            .viz-item {{ text-align: center; }}
            .viz-item a {{ display: block; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); text-decoration: none; color: white; border-radius: 8px; transition: transform 0.2s; }}
            .viz-item a:hover {{ transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }}
            h1, h2 {{ color: #333; }}
            .description {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 Point Deformable Mamba 可视化结果</h1>
            <div class="description">
                <p>本页面展示了Point Deformable Mamba模型中不同扫描方法的可视化结果。通过对比不同的扫描策略，可以更好地理解模型如何处理3D点云数据。</p>
            </div>

            <h2>📋 扫描方法说明</h2>
            <ul>
                <li><strong>希尔伯特曲线扫描 (Hilbert Curve)</strong>: 使用希尔伯特曲线对3D空间进行序列化，保持空间局部性</li>
                <li><strong>反希尔伯特扫描 (Hilbert-Trans)</strong>: 交换坐标轴后的希尔伯特曲线扫描，提供不同的空间遍历顺序</li>
                <li><strong>可变形扫描 (Deformable Scanning)</strong>: 基于点云特征自适应调整的扫描顺序，能够根据数据特点动态优化</li>
            </ul>
    """

    for sample_idx in range(num_samples):
        html_content += f"""
            <div class="sample-section">
                <h2>📊 样本 {sample_idx}</h2>
                <div class="visualization-grid">
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_original.html" target="_blank">🔵 原始点云</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert.html" target="_blank">🔴 希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_hilbert_trans.html" target="_blank">🟢 反希尔伯特扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_deformable.html" target="_blank">🟣 可变形扫描</a>
                    </div>
                    <div class="viz-item">
                        <a href="sample_{sample_idx}_comparison.html" target="_blank">📈 对比可视化</a>
                    </div>
                </div>
            </div>
        """

    html_content += """
            <div class="description">
                <h3>💡 使用说明</h3>
                <p>点击上方的链接打开对应的3D可视化页面。在可视化页面中，您可以：</p>
                <ul>
                    <li>🖱️ 拖拽旋转视角</li>
                    <li>🔍 滚轮缩放</li>
                    <li>📱 双击重置视角</li>
                    <li>🎨 观察不同扫描路径的颜色编码</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """

    with open(os.path.join(output_dir, "summary.html"), "w", encoding="utf-8") as f:
        f.write(html_content)

if __name__ == "__main__":
    main()
