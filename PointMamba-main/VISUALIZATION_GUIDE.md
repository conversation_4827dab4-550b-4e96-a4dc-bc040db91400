# Point Deformable Mamba 可视化完整指南

本指南提供了Point Deformable Mamba模型可视化工具的完整使用说明，包括交互式Web可视化和静态图片生成。

## 🎯 功能概览

### 1. 交互式Web可视化
- **文件**: `demo_visualization.py`
- **输出**: HTML文件，支持3D交互
- **特点**: 可旋转、缩放、实时交互

### 2. 静态图片生成
- **文件**: `generate_images.py`
- **输出**: 高分辨率PNG图片
- **特点**: 适合论文、报告、演示

### 3. 快速单样本生成
- **文件**: `quick_generate.py`
- **输出**: 单个样本的精美图片
- **特点**: 快速生成、高质量

## 📊 扫描方法说明

### 希尔伯特曲线扫描 (Hilbert Curve)
- **原理**: 使用希尔伯特曲线对3D空间进行序列化
- **特点**: 保持空间局部性，相邻点在序列中也相对接近
- **应用**: 适合处理具有空间连续性的点云数据

### 反希尔伯特扫描 (Hilbert-Trans)
- **原理**: 交换坐标轴后的希尔伯特曲线扫描
- **特点**: 提供不同的空间遍历顺序
- **应用**: 增加模型对不同空间方向的鲁棒性

### 可变形扫描 (Deformable Scanning)
- **原理**: 基于点云特征自适应调整的扫描顺序
- **特点**: 能够根据数据特点动态优化扫描路径
- **应用**: 提高模型对复杂几何结构的处理能力

## 🚀 快速开始

### 1. 生成交互式Web可视化

```bash
# 基本使用
python demo_visualization.py

# 自定义参数
python demo_visualization.py --num_samples 3 --output_dir my_output

# 查看结果
# 打开 demo_visualization_output/summary.html
```

### 2. 生成静态图片

```bash
# 生成多个样本的图片
python generate_images.py --num_samples 2 --max_points 150

# 查看结果
ls images_output/*.png
```

### 3. 快速生成单个样本

```bash
# 生成样本0的图片
python quick_generate.py --sample_idx 0 --max_points 100

# 生成不同样本
python quick_generate.py --sample_idx 5 --max_points 120
```

## 📁 输出文件结构

### Web可视化输出
```
demo_visualization_output/
├── summary.html                    # 总结页面
├── sample_0_original.html         # 原始点云
├── sample_0_hilbert.html          # 希尔伯特扫描
├── sample_0_hilbert_trans.html    # 反希尔伯特扫描
├── sample_0_deformable.html       # 可变形扫描
└── sample_0_comparison.html       # 对比可视化
```

### 图片输出
```
images_output/
├── README.md                       # 图片说明
├── sample_0_original.png          # 原始点云
├── sample_0_hilbert.png           # 希尔伯特扫描
├── sample_0_hilbert_trans.png     # 反希尔伯特扫描
├── sample_0_deformable.png        # 可变形扫描
└── sample_0_comparison.png        # 四方法对比
```

## 🎨 可视化特征说明

### 颜色编码
- **扫描路径**: 颜色从蓝/紫色到黄/红色，表示扫描顺序
- **起始点**: 绿色圆点标记扫描开始位置
- **结束点**: 红色方块标记扫描结束位置
- **连接线**: 红色线条显示扫描的连接路径

### 交互操作（Web版本）
- **旋转**: 鼠标左键拖拽
- **缩放**: 鼠标滚轮
- **平移**: 鼠标右键拖拽
- **重置**: 双击重置视角

## ⚙️ 参数配置

### 通用参数
- `--data_path`: 数据集路径
- `--num_samples`: 样本数量
- `--output_dir`: 输出目录

### 图片生成专用
- `--max_points`: 显示的最大点数
- `--sample_idx`: 特定样本索引

### 高级配置
```python
# 在脚本中可以调整的参数
figsize=(12, 10)        # 图片尺寸
dpi=300                 # 图片分辨率
elev=20, azim=45        # 3D视角
max_display=100         # 最大显示点数
```

## 🔧 故障排除

### 1. 数据集问题
```bash
# 检查数据集是否存在
ls data/ScanObjectNN/main_split_nobg/test_objectdataset.h5
```

### 2. 依赖包问题
```bash
# 安装必要的包
pip install plotly h5py matplotlib numpy torch
```

### 3. CUDA相关错误
- 可变形扫描模块需要CUDA支持
- 如果遇到CUDA错误，脚本会自动使用简化版本
- 不影响希尔伯特扫描的正常功能

### 4. 内存不足
- 减少`max_points`参数
- 减少`num_samples`参数
- 使用`quick_generate.py`处理单个样本

## 📈 使用建议

### 学术用途
- 使用`generate_images.py`生成高分辨率图片
- 设置`dpi=300`确保打印质量
- 使用对比图片展示不同方法的差异

### 教学演示
- 使用`demo_visualization.py`生成交互式可视化
- 学生可以自由旋转和缩放观察
- 使用summary.html作为导航页面

### 研究分析
- 使用`quick_generate.py`快速生成特定样本
- 调整`max_points`参数控制复杂度
- 比较不同样本的扫描模式

## 🎯 最佳实践

1. **首次使用**: 先运行`demo_visualization.py`了解基本功能
2. **论文图片**: 使用`generate_images.py`生成高质量图片
3. **快速预览**: 使用`quick_generate.py`快速查看特定样本
4. **批量处理**: 调整脚本参数处理多个样本
5. **自定义**: 修改脚本中的颜色、尺寸等参数

## 📞 技术支持

如遇到问题，请：
1. 检查数据集路径是否正确
2. 确认依赖包已正确安装
3. 查看终端输出的错误信息
4. 参考本指南的故障排除部分

---

**注意**: 本工具主要用于研究和教学目的，帮助理解Point Deformable Mamba模型的扫描机制。
